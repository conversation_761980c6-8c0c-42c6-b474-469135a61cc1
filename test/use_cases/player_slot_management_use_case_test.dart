import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/user.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/player_slot_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockServerRepository extends Mock implements ServerRepository {}
class MockUserManager extends Mock implements UserManager {}
class MockLoggingUseCase extends Mock implements LoggingUseCase {}
class MockRemoteLogger extends Mock implements RemoteLogger {}
class MockUserState extends Mock implements UserState {}

void main() {
  group('PlayerSlotManagementUseCase', () {
    late PlayerSlotManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockServerRepository mockServerRepository;
    late MockUserManager mockUserManager;
    late MockLoggingUseCase mockLoggingUseCase;
    late MockRemoteLogger mockLogger;
    late MockUserState mockUserState;
    late Map<String, MatchRepositoryInterface> repositories;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockServerRepository = MockServerRepository();
      mockUserManager = MockUserManager();
      mockLoggingUseCase = MockLoggingUseCase();
      mockLogger = MockRemoteLogger();
      mockUserState = MockUserState();
      
      repositories = {
        'local': mockLocalRepository,
        'network': mockNetworkRepository,
      };

      // Setup GetIt mock
      GetIt.instance.reset();
      GetIt.instance.registerSingleton<ServerRepository>(mockServerRepository);

      // Setup logging mock
      when(() => mockLoggingUseCase.getRemoteLogger(any()))
          .thenReturn(mockLogger);

      // Setup user manager mock
      when(() => mockUserManager.state).thenReturn(mockUserState);

      useCase = PlayerSlotManagementUseCase(
        repositories,
        mockUserManager,
        mockLoggingUseCase,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('updatePlayerSlots', () {
      test('should update player slots successfully', () async {
        // Arrange
        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final updatedSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final request = UpdatePlayerSlotsRequest(
          playerSlots: updatedSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerSlots(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.updatedSlots, equals(updatedSlots));
      });

      test('should update server when match is selected and network type', () async {
        // Arrange
        final user = User(id: 'user123');
        when(() => mockUserState.user).thenReturn(user);
        
        final updatedSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final request = UpdatePlayerSlotsRequest(
          playerSlots: updatedSlots,
          repositoryType: 'network',
          isSelectedMatch: true,
          matchId: 'match123',
        );

        when(() => mockServerRepository.updateMatchPlayerSlots(any(), any(), any()))
            .thenAnswer((_) async => true);

        // Act
        final result = await useCase.updatePlayerSlots(request);

        // Assert
        expect(result.isSuccess, isTrue);
        verify(() => mockServerRepository.updateMatchPlayerSlots(
          'match123',
          updatedSlots,
          'user123',
        )).called(1);
      });
    });

    group('updatePlayerType', () {
      test('should update player type successfully', () async {
        // Arrange
        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final request = UpdatePlayerTypeRequest(
          slotIndex: 0,
          newPlayerType: PlayerType.humanNetwork,
          currentSlots: originalSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerType(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.updatedSlots[0].type, equals(PlayerType.humanNetwork));
      });

      test('should handle invalid slot index', () async {
        // Arrange
        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final request = UpdatePlayerTypeRequest(
          slotIndex: 5, // Invalid index
          newPlayerType: PlayerType.humanNetwork,
          currentSlots: originalSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerType(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Invalid slot index'));
      });
    });

    group('joinPlayerSlot', () {
      test('should join player slot successfully', () async {
        // Arrange
        final user = User(id: 'user123');
        when(() => mockUserState.user).thenReturn(user);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.updatedSlots[0].playerId, equals('user123'));
      });

      test('should fail when no user is available', () async {
        // Arrange
        when(() => mockUserState.user).thenReturn(null);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('No user available'));
      });

      test('should fail when slot is already occupied', () async {
        // Arrange
        final user = User(id: 'user123');
        when(() => mockUserState.user).thenReturn(user);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'other_player',
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Occupied Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          repositoryType: 'local',
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Slot is already occupied'));
      });
    });

    group('createDefaultSlots', () {
      test('should create default slots for game config', () {
        // Arrange
        final config = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        // Act
        final result = useCase.createDefaultSlots(config);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(2)); // Default minimum slots
        expect(result[0].id, equals('slot_0'));
        expect(result[1].id, equals('slot_1'));
      });
    });

    group('canAddPlayerSlot', () {
      test('should allow adding slot when under maximum', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        // Act
        final result = useCase.canAddPlayerSlot(currentSlots, config);

        // Assert
        expect(result, isTrue);
      });
    });

    group('canRemovePlayerSlot', () {
      test('should allow removing slot when above minimum', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'player2',
            type: PlayerType.humanLocal,
            playerClassId: 'class2',
            name: 'Player 2',
          ),
        ];

        final config = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        // Act
        final result = useCase.canRemovePlayerSlot(currentSlots, 1, config);

        // Assert
        expect(result, isTrue);
      });

      test('should not allow removing slot with invalid index', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        // Act
        final result = useCase.canRemovePlayerSlot(currentSlots, 5, config);

        // Assert
        expect(result, isFalse);
      });
    });
  });
}
