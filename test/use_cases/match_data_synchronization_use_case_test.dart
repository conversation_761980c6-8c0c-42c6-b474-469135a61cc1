import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockLoggingUseCase extends Mock implements LoggingUseCase {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

void main() {
  group('MatchDataSynchronizationUseCase', () {
    late MatchDataSynchronizationUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockLoggingUseCase mockLoggingUseCase;
    late MockRemoteLogger mockLogger;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockNetworkRepository();
      mockLoggingUseCase = MockLoggingUseCase();
      mockLogger = MockRemoteLogger();

      // Setup logging mock
      when(() => mockLoggingUseCase.getRemoteLogger(any()))
          .thenReturn(mockLogger);

      // Reset GetIt
      GetIt.instance.reset();

      useCase = MatchDataSynchronizationUseCase(
        mockLogger,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('loadMatchData', () {
      test('should load matches from local repository only', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        final localMatches = [
          _createTestMatch('local_match_1', 'Local Match 1'),
          _createTestMatch('local_match_2', 'Local Match 2'),
        ];

        when(() => mockLocalRepository.getOpenMatches(any()))
            .thenAnswer((_) async => localMatches);

        final request = LoadMatchDataRequest(gameName: 'test_game');

        // Act
        final result = await useCase.loadMatchData(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.allMatches, hasLength(2));
        expect(result.data!.matchesBySource['local'], hasLength(2));
        expect(result.data!.sourceCount, equals(1));
      });

      test('should load matches from both local and network repositories', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        final localMatches = [
          _createTestMatch('local_match_1', 'Local Match 1'),
        ];

        final networkMatches = [
          _createTestMatch('network_match_1', 'Network Match 1'),
          _createTestMatch('network_match_2', 'Network Match 2'),
        ];

        when(() => mockLocalRepository.getOpenMatches(any()))
            .thenAnswer((_) async => localMatches);
        when(() => mockNetworkRepository.getOpenMatches(any()))
            .thenAnswer((_) async => networkMatches);

        final request = LoadMatchDataRequest(gameName: 'test_game');

        // Act
        final result = await useCase.loadMatchData(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.allMatches, hasLength(3));
        expect(result.data!.matchesBySource['local'], hasLength(1));
        expect(result.data!.matchesBySource['network'], hasLength(2));
        expect(result.data!.sourceCount, equals(2));
      });

      test('should deduplicate matches with same ID', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        final duplicateMatch = _createTestMatch('duplicate_match', 'Duplicate Match');
        final localMatches = [duplicateMatch];
        final networkMatches = [duplicateMatch]; // Same match in both sources

        when(() => mockLocalRepository.getOpenMatches(any()))
            .thenAnswer((_) async => localMatches);
        when(() => mockNetworkRepository.getOpenMatches(any()))
            .thenAnswer((_) async => networkMatches);

        final request = LoadMatchDataRequest(gameName: 'test_game');

        // Act
        final result = await useCase.loadMatchData(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.allMatches, hasLength(1)); // Deduplicated
        expect(result.data!.sourceCount, equals(2));
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        when(() => mockLocalRepository.getOpenMatches(any()))
            .thenThrow(Exception('Repository error'));

        final request = LoadMatchDataRequest(gameName: 'test_game');

        // Act
        final result = await useCase.loadMatchData(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.allMatches, isEmpty);
        expect(result.data!.sourceCount, equals(0));
      });
    });

    group('refreshMatchesFromSource', () {
      test('should refresh matches from specific source', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        final refreshedMatches = [
          _createTestMatch('refreshed_match_1', 'Refreshed Match 1'),
        ];

        when(() => mockLocalRepository.getOpenMatches(any()))
            .thenAnswer((_) async => refreshedMatches);

        final request = RefreshMatchesRequest(sourceName: 'local');

        // Act
        final result = await useCase.refreshMatchesFromSource(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.matches, hasLength(1));
        expect(result.data!.sourceName, equals('local'));
      });

      test('should fail when source is not available', () async {
        // Arrange
        final request = RefreshMatchesRequest(sourceName: 'nonexistent');

        // Act
        final result = await useCase.refreshMatchesFromSource(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Source nonexistent not available'));
      });
    });

    group('subscribeToRealTimeUpdates', () {
      test('should subscribe to real-time updates successfully', () async {
        // Arrange
        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);

        final request = SubscribeToRealTimeUpdatesRequest();

        // Act
        final result = await useCase.subscribeToRealTimeUpdates(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should handle no real-time capable repositories', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);

        final request = SubscribeToRealTimeUpdatesRequest();

        // Act
        final result = await useCase.subscribeToRealTimeUpdates(request);

        // Assert
        expect(result.isSuccess, isTrue); // Should succeed but log that no real-time updates available
      });
    });

    group('unsubscribeFromRealTimeUpdates', () {
      test('should unsubscribe from real-time updates successfully', () async {
        // Arrange
        final request = UnsubscribeFromRealTimeUpdatesRequest();

        // Act
        final result = await useCase.unsubscribeFromRealTimeUpdates(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });
    });

    group('deduplicateMatches', () {
      test('should deduplicate matches correctly', () {
        // Arrange
        final match1 = _createTestMatch('match_1', 'Match 1');
        final match2 = _createTestMatch('match_2', 'Match 2');
        final match1Duplicate = _createTestMatch('match_1', 'Match 1 Duplicate');

        final matchesBySource = {
          'local': [match1, match2],
          'network': [match1Duplicate], // Duplicate of match1
        };

        // Act
        final result = useCase.deduplicateMatches(matchesBySource);

        // Assert
        expect(result, hasLength(2)); // Should have only 2 unique matches
        expect(result.map((m) => m.id), containsAll(['match_1', 'match_2']));
      });

      test('should handle empty matches', () {
        // Arrange
        final matchesBySource = <String, List<GameMatch>>{};

        // Act
        final result = useCase.deduplicateMatches(matchesBySource);

        // Assert
        expect(result, isEmpty);
      });
    });
  });
}

// Helper function to create test matches
GameMatch _createTestMatch(String id, String name) {
  return GameMatch(
    id: id,
    gameTypeId: 'test_game',
    creatorId: 'test_creator',
    gameName: name,
    createdAt: DateTime.now().millisecondsSinceEpoch,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
    status: MatchStatus.open,
    isOpenForJoining: true,
    playerSlots: [
      PlayerSlot(
        id: 'slot_0',
        playerId: 'player1',
        type: PlayerType.humanLocal,
        playerClassId: 'class1',
        name: 'Player 1',
      ),
    ],
    players: [],
    turns: [],
    currentTurn: 0,
  );
}

// Additional mock class for network repository
class MockNetworkRepository extends Mock implements MatchRepositoryInterface {}
