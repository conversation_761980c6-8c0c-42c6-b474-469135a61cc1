import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_source_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockLoggingUseCase extends Mock implements LoggingUseCase {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

void main() {
  group('MatchSourceManagementUseCase', () {
    late MatchSourceManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockLoggingUseCase mockLoggingUseCase;
    late MockRemoteLogger mockLogger;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockNetworkRepository();
      mockLoggingUseCase = MockLoggingUseCase();
      mockLogger = MockRemoteLogger();

      // Setup logging mock
      when(() => mockLoggingUseCase.getRemoteLogger(any()))
          .thenReturn(mockLogger);

      // Reset GetIt
      GetIt.instance.reset();

      useCase = MatchSourceManagementUseCase(
        mockLoggingUseCase,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('initializeRepositories', () {
      test('should initialize local repository successfully', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Act
        final result = await useCase.initializeRepositories(
          InitializeRepositoriesRequest(),
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.availableSources, contains('local'));
        expect(result.data!.hasNetworkCapability, isFalse);
      });

      test('should initialize both local and network repositories when in server scope', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Create a server-connected scope
        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = await useCase.initializeRepositories(
          InitializeRepositoriesRequest(),
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.availableSources, contains('local'));
        expect(result.data!.availableSources, contains('network'));
        expect(result.data!.hasNetworkCapability, isTrue);
      });

      test('should handle missing local repository gracefully', () async {
        // Arrange - Don't register any repositories

        // Act
        final result = await useCase.initializeRepositories(
          InitializeRepositoriesRequest(),
        );

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.availableSources, isEmpty);
        expect(result.data!.hasNetworkCapability, isFalse);
      });
    });

    group('cleanupRepositories', () {
      test('should cleanup repositories successfully', () async {
        // Arrange
        final request = CleanupRepositoriesRequest(
          repositoriesToCleanup: ['local', 'network'],
        );

        // Act
        final result = await useCase.cleanupRepositories(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should handle cleanup errors gracefully', () async {
        // Arrange
        final request = CleanupRepositoriesRequest(
          repositoriesToCleanup: ['nonexistent'],
        );

        // Act
        final result = await useCase.cleanupRepositories(request);

        // Assert
        expect(result.isSuccess, isTrue); // Should not fail for missing repositories
      });
    });

    group('getAvailableSources', () {
      test('should return available sources correctly', () {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Act
        final result = useCase.getAvailableSources();

        // Assert
        expect(result, contains('local'));
        expect(result, isNot(contains('network')));
      });

      test('should return both sources when network is available', () async {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = useCase.getAvailableSources();

        // Assert
        expect(result, contains('local'));
        expect(result, contains('network'));
      });
    });

    group('hasNetworkCapability', () {
      test('should return false when no network repository', () {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isFalse);
      });

      test('should return true when network repository is available', () async {
        // Arrange
        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isTrue);
      });
    });

    group('handleServerScopeChange', () {
      test('should handle server scope becoming available', () async {
        // Arrange
        final request = HandleServerScopeChangeRequest(
          isServerScopeAvailable: true,
        );

        // Act
        final result = await useCase.handleServerScopeChange(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.scopeChangeHandled, isTrue);
      });

      test('should handle server scope becoming unavailable', () async {
        // Arrange
        final request = HandleServerScopeChangeRequest(
          isServerScopeAvailable: false,
        );

        // Act
        final result = await useCase.handleServerScopeChange(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.scopeChangeHandled, isTrue);
      });
    });

    group('getSourceCapabilities', () {
      test('should return correct capabilities for local source', () {
        // Arrange
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        when(() => mockLocalRepository.supportsMatchCreation).thenReturn(true);

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result, containsPair('local', isA<Map<String, bool>>()));
        expect(result['local']!['supportsRealTimeUpdates'], isFalse);
        expect(result['local']!['supportsMatchCreation'], isTrue);
      });

      test('should return correct capabilities for network source', () async {
        // Arrange
        await GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        when(() => mockNetworkRepository.supportsMatchCreation).thenReturn(true);

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result, containsPair('network', isA<Map<String, bool>>()));
        expect(result['network']!['supportsRealTimeUpdates'], isTrue);
        expect(result['network']!['supportsMatchCreation'], isTrue);
      });
    });

    group('removeMatchSource', () {
      test('should remove match source successfully', () async {
        // Arrange
        final request = RemoveMatchSourceRequest(
          sourceName: 'network',
        );

        // Act
        final result = await useCase.removeMatchSource(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.sourceRemoved, isTrue);
        expect(result.data!.removedSourceName, equals('network'));
      });

      test('should handle removing non-existent source', () async {
        // Arrange
        final request = RemoveMatchSourceRequest(
          sourceName: 'nonexistent',
        );

        // Act
        final result = await useCase.removeMatchSource(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data!.sourceRemoved, isTrue); // Should succeed even if source doesn't exist
      });
    });
  });
}

// Additional mock class for network repository
class MockNetworkRepository extends Mock implements MatchRepositoryInterface {}
