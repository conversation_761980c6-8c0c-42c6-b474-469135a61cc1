import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/user.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockUserManager extends Mock implements UserManager {}
class MockLoggingUseCase extends Mock implements LoggingUseCase {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

void main() {
  group('MatchManagementUseCase', () {
    late MatchManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockUserManager mockUserManager;
    late MockLoggingUseCase mockLoggingUseCase;
    late MockRemoteLogger mockLogger;
    late Map<String, MatchRepositoryInterface> repositories;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockUserManager = MockUserManager();
      mockLoggingUseCase = MockLoggingUseCase();
      mockLogger = MockRemoteLogger();

      repositories = {
        'local': mockLocalRepository,
        'network': mockNetworkRepository,
      };

      // Setup logging mock
      when(() => mockLoggingUseCase.getRemoteLogger(any()))
          .thenReturn(mockLogger);

      useCase = MatchManagementUseCase(
        repositories,
        mockUserManager,
        mockLogger,
      );
    });

    group('createMatch', () {
      test('should create match successfully with valid request', () async {
        // Arrange
        final gameConfig = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final expectedMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          gameName: 'Test Match',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          status: GameMatchStatus.open,
          isOpenForJoining: true,
          playerSlots: request.playerSlots,
          players: [],
          turns: [],
          currentTurn: 0,
        );

        when(() => mockLocalRepository.createMatch(any(), any()))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, equals(expectedMatch));
        verify(() => mockLocalRepository.createMatch(any(), any())).called(1);
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        final gameConfig = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [],
        );

        when(() => mockLocalRepository.createMatch(any(), any()))
            .thenThrow(Exception('Repository error'));

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Failed to create match'));
      });
    });

    group('validateCreateMatchRequest', () {
      test('should validate successfully with valid request', () {
        // Arrange
        final gameConfig = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Valid Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        // Act
        final result = useCase.validateCreateMatchRequest(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should fail validation with empty game name', () {
        // Arrange
        final gameConfig = GameConfig(
          gameTypeId: 'test_game',
          gameName: 'Test Game',
          description: 'A test game',
          version: '1.0.0',
          isActive: true,
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: '',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        // Act
        final result = useCase.validateCreateMatchRequest(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Game name is required'));
      });
    });
  });
}
