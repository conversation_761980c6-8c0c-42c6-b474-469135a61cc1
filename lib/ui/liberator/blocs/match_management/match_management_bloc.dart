import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';

import 'package:dauntless/models/base/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';

import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:dauntless/use_cases/match_management_use_case.dart';
import 'package:dauntless/use_cases/player_slot_management_use_case.dart';
import 'package:dauntless/use_cases/match_source_management_use_case.dart';
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'match_management_event.dart';
import 'match_management_state.dart';

/// Consolidated BLoC for match management
/// Combines functionality from MatchSelectionBloc, CreateMatchBloc, and MatchSelectionEnvironmentManager
/// Uses repository interfaces from Phase 1 for data access
class MatchManagementBloc extends Bloc<MatchManagementEvent, MatchManagementState> {
  // ========================================================================
  // DEPENDENCIES
  // ========================================================================

  final GameConfigUseCase _gameConfigUseCase;
  final RemoteLogger _logger;

  // New use cases for business logic
  final MatchManagementUseCase _matchManagementUseCase;
  final PlayerSlotManagementUseCase _playerSlotManagementUseCase;
  final MatchSourceManagementUseCase _matchSourceManagementUseCase;
  final MatchDataSynchronizationUseCase _matchDataSynchronizationUseCase;
  
  // Repository interfaces for different data sources
  final Map<String, MatchRepositoryInterface> _matchRepositories = {};
  // Removed unused _persistenceRepository field
  
  // Stream subscriptions for real-time updates
  final Map<String, StreamSubscription> _sourceSubscriptions = {};

  // WebSocket integration for real-time match updates
  StreamSubscription<List<GameMatch>>? _openMatchesSubscription;
  StreamSubscription<List<GameMatch>>? _webSocketSubscription;
  
  // ========================================================================
  // CONSTRUCTOR
  // ========================================================================
  
  MatchManagementBloc(
    this._gameConfigUseCase,
    this._logger,
    this._matchManagementUseCase,
    this._playerSlotManagementUseCase,
    this._matchSourceManagementUseCase,
    this._matchDataSynchronizationUseCase,
  ) : super(const MatchManagementState()) {
    // Initialize event handlers
    _registerEventHandlers();

    // Don't initialize repositories in constructor - do it lazily when needed
    // This prevents dependency injection order issues
  }

  // ========================================================================
  // EVENT HANDLER REGISTRATION
  // ========================================================================
  
  void _registerEventHandlers() {
    // Initialization events
    on<InitializeMatchManagementEvent>(_onInitialize);
    on<LoadAvailableMatchSourcesEvent>(_onLoadAvailableMatchSources);
    
    // Match discovery & selection events
    on<LoadMatchDataEvent>(_onLoadMatchData);
    on<RefreshMatchesFromSourceEvent>(_onRefreshMatchesFromSource);
    on<SelectMatchEvent>(_onSelectMatch);
    on<ClearSelectedMatchEvent>(_onClearSelectedMatch);
    
    // Match joining events
    on<JoinSelectedMatchEvent>(_onJoinSelectedMatch);
    on<JoinMatchEvent>(_onJoinMatch);
    on<LeaveMatchEvent>(_onLeaveMatch);
    
    // Match creation events
    on<StartMatchCreationEvent>(_onStartMatchCreation);
    on<CancelMatchCreationEvent>(_onCancelMatchCreation);
    on<SelectMatchConfigEvent>(_onSelectMatchConfig);
    on<UpdateGameNameEvent>(_onUpdateGameName);
    
    // Player slot management events
    on<AddPlayerSlotEvent>(_onAddPlayerSlot);
    on<RemovePlayerSlotEvent>(_onRemovePlayerSlot);
    on<UpdatePlayerTypeEvent>(_onUpdatePlayerType);
    on<UpdateSelectedMatchPlayerTypeEvent>(_onUpdateSelectedMatchPlayerType);
    on<UpdatePlayerNameEvent>(_onUpdatePlayerName);
    on<SetHostPlayerEvent>(_onSetHostPlayer);
    on<JoinPlayerSlotEvent>(_onJoinPlayerSlot);
    on<JoinSelectedMatchSlotEvent>(_onJoinSelectedMatchSlot);
    
    // Match lifecycle events
    on<CreateAndStartMatchEvent>(_onCreateAndStartMatch);
    on<DeleteMatchEvent>(_onDeleteMatch);
    
    // Real-time monitoring events
    on<SubscribeToMatchUpdatesEvent>(_onSubscribeToMatchUpdates);
    on<UnsubscribeFromMatchUpdatesEvent>(_onUnsubscribeFromMatchUpdates);
    
    // Source management events
    on<AddMatchSourceEvent>(_onAddMatchSource);
    on<RemoveMatchSourceEvent>(_onRemoveMatchSource);
    
    // Network & capability events
    on<NetworkCapabilityChangedEvent>(_onNetworkCapabilityChanged);
    
    // UI state events
    on<ToggleServerProfileSelectorEvent>(_onToggleServerProfileSelector);

    // Internal update events
    on<HandleMatchUpdatesFromSourceEvent>(_onHandleMatchUpdatesFromSource);
    on<HandleWebSocketMatchUpdateEvent>(_onHandleWebSocketMatchUpdate);
    on<ServerScopeAvailableEvent>(_onServerScopeAvailable);
    on<ServerScopeLostEvent>(_onServerScopeLost);
  }

  // ========================================================================
  // REPOSITORY INITIALIZATION
  // ========================================================================


  
  void _initializeRepositories() {
    try {
      // Clear existing repositories to prevent duplicates
      _matchRepositories.clear();

      // Get local repository (should always be available)
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        _matchRepositories['local'] = localRepo;
        _logger.info('Initialized local match repository');
      } catch (e) {
        _logger.error('Failed to get local repository: $e');
        // Don't throw - continue without local repository
      }

      // Try to get network repository (only available when server connected)
      _logger.info('Checking for network repository, current scope: ${GetIt.I.currentScopeName}');
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _matchRepositories['network'] = networkRepo;
          _logger.info('Initialized network match repository');
        } catch (e) {
          _logger.warn('Network match repository not available: $e');
        }
      } else {
        _logger.info('Network repository not available - not in server-connected scope');
      }

    } catch (e) {
      _logger.error('Failed to initialize repositories: $e');
    }
  }

  // ========================================================================
  // INITIALIZATION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onInitialize(
    InitializeMatchManagementEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      _logger.info('Initializing MatchManagementBloc');

      // Initialize repositories
      final repoResult = await _matchSourceManagementUseCase.initializeRepositories();
      if (repoResult.isFailure) {
        emit(state.setError(repoResult.error));
        return;
      }

      // Load available game configurations
      final configs = await _gameConfigUseCase.loadLocalGameConfigs();

      final repoData = repoResult.value;
      emit(state.copyWith(
        availableConfigs: configs,
        availableMatchSources: repoData.availableSources,
        hasNetworkCapability: repoData.hasNetworkCapability,
        processingStatus: ProcessingStatus.loaded,
      ));

      _logger.info('MatchManagementBloc initialized with ${configs.length} configs and ${repoData.availableSources.length} sources');

    } catch (e) {
      _logger.error('Failed to initialize MatchManagementBloc: $e');
      emit(state.setError('Failed to initialize: $e'));
    }
  }

  Future<void> _onLoadAvailableMatchSources(
    LoadAvailableMatchSourcesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    try {
      final availableSources = _matchRepositories.keys.toList();
      emit(state.copyWith(availableMatchSources: availableSources));
      _logger.info('Loaded ${availableSources.length} available match sources');
    } catch (e) {
      _logger.error('Failed to load available match sources: $e');
      emit(state.setError('Failed to load match sources: $e'));
    }
  }

  // ========================================================================
  // MATCH DISCOVERY & SELECTION EVENT HANDLERS
  // ========================================================================
  
  Future<void> _onLoadMatchData(
    LoadMatchDataEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = LoadMatchDataRequest(
      gameName: event.gameName,
      forceRefresh: false,
    );

    final result = await _matchDataSynchronizationUseCase.loadMatchData(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (matchData) {
        emit(state.copyWith(
          openMatches: matchData.allMatches,
          matchesBySource: matchData.matchesBySource,
          availableMatchSources: matchData.availableSources,
          hasNetworkCapability: matchData.hasNetworkCapability,
          processingStatus: ProcessingStatus.loaded,
        ));
      },
    );
  }

  Future<void> _onRefreshMatchesFromSource(
    RefreshMatchesFromSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final sourceName = event.sourceName;
    final repository = _matchRepositories[sourceName];

    if (repository == null) {
      _logger.warn('Repository not found for source: $sourceName');
      return;
    }

    try {
      final gameName = 'default'; // TODO: Get from current game context
      final matches = await repository.fetchOpenMatches(gameName);

      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
      updatedMatchesBySource[sourceName] = matches;

      // Rebuild the complete matches list
      final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

      emit(state.copyWith(
        openMatches: allMatches,
        matchesBySource: updatedMatchesBySource,
        errorMessage: null, // Clear any previous errors on successful refresh
      ));

      _logger.info('Refreshed ${matches.length} matches from $sourceName');

    } catch (e) {
      _logger.error('Failed to refresh matches from $sourceName: $e');

      // For local sources, handle errors gracefully by showing empty state
      if (_isLocalSource(sourceName)) {
        _logger.info('Handling local source error gracefully, showing empty state');
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource[sourceName] = []; // Show empty list for local sources

        // Rebuild the complete matches list
        final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

        emit(state.copyWith(
          openMatches: allMatches,
          matchesBySource: updatedMatchesBySource,
          errorMessage: null, // Don't show error for local sources
        ));
      } else {
        // For network sources, show the error
        emit(state.setError('Failed to refresh matches from $sourceName: $e'));
      }
    }
  }

  void _onSelectMatch(
    SelectMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Match selected: ${event.match.id}');
    
    emit(state.copyWith(
      selectedMatch: event.match,
      currentMode: 'selection',
    ));
  }

  void _onClearSelectedMatch(
    ClearSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Clearing selected match: ${event.matchId ?? 'current'}');
    
    emit(state.copyWith(
      selectedMatch: null,
    ));
  }

  // ========================================================================
  // MATCH JOINING EVENT HANDLERS
  // ========================================================================

  Future<void> _onJoinSelectedMatch(
    JoinSelectedMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    emit(state.setLoading());

    try {
      final match = state.selectedMatch!;
      final success = await _joinMatchById(match.id);

      if (success) {
        _logger.info('Successfully joined match ${match.id}');
        emit(state.setLoaded());
      } else {
        emit(state.setError('Failed to join match'));
      }
    } catch (e) {
      _logger.error('Error joining selected match: $e');
      emit(state.setError('Error joining match: $e'));
    }
  }

  Future<void> _onJoinMatch(
    JoinMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final success = await _joinMatchById(event.matchId, playerId: event.playerId);

      if (success) {
        _logger.info('✅ JOIN SUCCESS: Joined match ${event.matchId}, waiting for WebSocket update...');
        emit(state.setLoaded());
      } else {
        _logger.error('❌ JOIN FAILED: Could not join match ${event.matchId}');
        emit(state.setError('Failed to join match'));
      }
    } catch (e) {
      _logger.error('Error joining match ${event.matchId}: $e');
      emit(state.setError('Error joining match: $e'));
    }
  }

  Future<void> _onLeaveMatch(
    LeaveMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    try {
      final success = await _leaveMatchById(event.matchId, playerId: event.playerId);

      if (success) {
        _logger.info('Successfully left match ${event.matchId}');
        emit(state.setLoaded());
      } else {
        emit(state.setError('Failed to leave match'));
      }
    } catch (e) {
      _logger.error('Error leaving match ${event.matchId}: $e');
      emit(state.setError('Error leaving match: $e'));
    }
  }

  // ========================================================================
  // MATCH CREATION EVENT HANDLERS
  // ========================================================================

  void _onStartMatchCreation(
    StartMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Starting match creation');

    // Use PlayerSlotManagementUseCase to create default slots
    List<PlayerSlot> defaultPlayerSlots = [];
    GameConfig? defaultConfig;

    if (state.availableConfigs.isNotEmpty) {
      defaultConfig = state.availableConfigs.first;
      defaultPlayerSlots = _playerSlotManagementUseCase.createDefaultSlots(defaultConfig);
    } else {
      // Fallback if no configs available - create minimal default config
      defaultConfig = GameConfig(
        id: 'default',
        name: 'Default Game',
      );
      defaultPlayerSlots = _playerSlotManagementUseCase.createDefaultSlots(defaultConfig);
    }

    emit(state.enterCreationMode().copyWith(
      selectedConfigId: defaultConfig.id,
      gameName: defaultConfig.name,
      matchId: null,
      playerSlots: defaultPlayerSlots,
    ));
  }

  void _onCancelMatchCreation(
    CancelMatchCreationEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Cancelling match creation');

    emit(state.enterSelectionMode().copyWith(
      selectedConfigId: null,
      gameName: null,
      matchId: null,
      playerSlots: [],
    ));
  }

  void _onSelectMatchConfig(
    SelectMatchConfigEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Selected match config: ${event.matchConfigId}');

    emit(state.copyWith(
      selectedConfigId: event.matchConfigId,
    ));
  }

  void _onUpdateGameName(
    UpdateGameNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    emit(state.copyWith(gameName: event.gameName));
  }

  // ========================================================================
  // PLAYER SLOT MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddPlayerSlot(
    AddPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // Get the current config for validation
    final config = state.availableConfigs.firstWhere(
      (c) => c.id == state.selectedConfigId,
      orElse: () => GameConfig(
        id: 'default',
        name: 'Default Game',
      ),
    );

    final result = _playerSlotManagementUseCase.addPlayerSlot(state.playerSlots, config);

    if (result.isSuccess) {
      emit(state.copyWith(playerSlots: result.value));
      _logger.info('Added player slot successfully');
    } else {
      emit(state.setError(result.error));
      _logger.warn('Failed to add player slot: ${result.error}');
    }
  }

  void _onRemovePlayerSlot(
    RemovePlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final result = _playerSlotManagementUseCase.removePlayerSlot(state.playerSlots, event.slotIndex);

    if (result.isSuccess) {
      emit(state.copyWith(playerSlots: result.value));
      _logger.info('Removed player slot at index ${event.slotIndex}');
    } else {
      emit(state.setError(result.error));
      _logger.warn('Failed to remove player slot: ${result.error}');
    }
  }

  Future<void> _onUpdatePlayerType(
    UpdatePlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = UpdatePlayerTypeRequest(
      slotIndex: event.slotIndex,
      newType: event.playerType,
      currentSlots: state.playerSlots,
      gameConfig: state.selectedConfig!,
      matchId: state.matchId,
      isSelectedMatch: false,
    );

    final result = await _playerSlotManagementUseCase.updatePlayerType(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (updateResult) {
        emit(state.copyWith(
          playerSlots: updateResult.updatedSlots,
          processingStatus: ProcessingStatus.loaded,
        ));

        // If a network match was created, trigger a match data reload
        if (updateResult.requiresNetworkMatch && updateResult.updatedMatch != null) {
          emit(state.copyWith(
            matchId: updateResult.updatedMatch!.id,
            selectedMatch: updateResult.updatedMatch,
          ));
          add(const LoadMatchDataEvent());
        }
      },
    );
  }

  Future<void> _onUpdateSelectedMatchPlayerType(
    UpdateSelectedMatchPlayerTypeEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final selectedMatch = state.selectedMatch;
    if (selectedMatch == null) {
      _logger.warn('No selected match to update player type');
      return;
    }

    if (event.slotIndex < 0 || event.slotIndex >= selectedMatch.playerSlots.length) {
      _logger.warn('Invalid slot index for selected match type update: ${event.slotIndex}');
      return;
    }

    // Create updated player slots for the selected match
    final updatedSlots = [...selectedMatch.playerSlots];
    final oldSlot = updatedSlots[event.slotIndex];
    updatedSlots[event.slotIndex] = oldSlot.copyWith(
      type: event.playerType,
    );

    // Create updated match with new player slots
    final updatedMatch = selectedMatch.copyWith(
      playerSlots: updatedSlots,
    );

    // Update the state with the modified selected match
    emit(state.copyWith(
      selectedMatch: updatedMatch,
      processingStatus: ProcessingStatus.loading,
    ));

    _logger.info('Updated player type for selected match slot ${event.slotIndex}: ${event.playerType}');

    // Check if this is a network match and update the server
    final isNetworkMatch = _isNetworkMatch(selectedMatch, state);
    _logger.info('Server update check: isNetworkMatch=$isNetworkMatch');

    // Check if we can access the server repository (actual server connectivity)
    bool canUpdateServer = false;
    try {
      GetIt.I<ServerRepository>(); // Check if available
      canUpdateServer = true;
      _logger.info('ServerRepository is available for updates');
    } catch (e) {
      _logger.warn('ServerRepository not available for updates: $e');
    }

    _logger.info('Server update decision: isNetworkMatch=$isNetworkMatch, canUpdateServer=$canUpdateServer');

    if (isNetworkMatch && canUpdateServer) {
      _logger.info('Attempting server update for match ${selectedMatch.id}');
      try {
        // Get current user ID for the update
        final user = GetIt.I<UserManager>().state.user;
        if (user == null) {
          throw Exception('No user available for server update');
        }

        // Update the server with the new player slots
        final serverRepository = GetIt.I<ServerRepository>();
        final success = await serverRepository.updateMatchPlayerSlots(
          selectedMatch.id,
          updatedSlots,
          user.id,
        );

        if (success) {
          _logger.info('Successfully updated player type on server for match ${selectedMatch.id}');
          emit(state.copyWith(
            selectedMatch: updatedMatch,
            processingStatus: ProcessingStatus.loaded,
          ));
        } else {
          throw Exception('Server rejected player type update');
        }
      } catch (e) {
        _logger.error('Failed to update player type on server: $e');
        // Keep the local update but show error status
        emit(state.copyWith(
          selectedMatch: updatedMatch,
          processingStatus: ProcessingStatus.error,
          errorMessage: 'Failed to update server match: $e',
        ));
      }
    } else {
      // For local matches or when network not available, just update local state
      _logger.info('Skipping server update: local match or server unavailable');
      emit(state.copyWith(
        selectedMatch: updatedMatch,
        processingStatus: ProcessingStatus.loaded,
      ));
    }
  }

  void _onUpdatePlayerName(
    UpdatePlayerNameEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for name update: ${event.slotIndex}');
      return;
    }

    final updatedSlots = [...state.playerSlots];
    updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
      name: event.name,
    );

    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('Updated player name for slot ${event.slotIndex}: ${event.name}');
  }

  void _onSetHostPlayer(
    SetHostPlayerEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // For now, we'll track the host by keeping the host slot as the first slot
    // or by using a separate hostPlayerId field in the state
    // Since PlayerSlot doesn't have isHost, we'll handle this in the UI layer

    _logger.info('Set host player: ${event.slotId}');

    // TODO: Implement host tracking logic when PlayerSlot model supports it
    // For now, just log the event
  }

  void _onJoinPlayerSlot(
    JoinPlayerSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    if (event.slotIndex < 0 || event.slotIndex >= state.playerSlots.length) {
      _logger.warn('Invalid slot index for join: ${event.slotIndex}');
      return;
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      emit(state.setError('No user logged in'));
      return;
    }

    final updatedSlots = [...state.playerSlots];
    updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
      playerId: user.id,
      name: user.id, // User model doesn't have name property, using id for now
    );

    emit(state.copyWith(playerSlots: updatedSlots));

    _logger.info('User ${user.id} joined slot ${event.slotIndex}');
  }

  /// Join the current user to a specific slot in the selected match (with server update)
  Future<void> _onJoinSelectedMatchSlot(
    JoinSelectedMatchSlotEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    if (state.selectedMatch == null) {
      emit(state.setError('No match selected'));
      return;
    }

    final user = GetIt.I<UserManager>().state.user;
    if (user == null) {
      emit(state.setError('No user logged in'));
      return;
    }

    final selectedMatch = state.selectedMatch!;
    final currentUserId = user.id;

    if (event.slotIndex < 0 || event.slotIndex >= selectedMatch.playerSlots.length) {
      emit(state.setError('Invalid slot index: ${event.slotIndex}'));
      return;
    }

    emit(state.setLoading());

    try {
      // Create updated player slots with slot switching logic
      final updatedSlots = [...selectedMatch.playerSlots];

      // First, remove the user from any existing slots (slot switching)
      for (int i = 0; i < updatedSlots.length; i++) {
        if (updatedSlots[i].playerId == currentUserId) {
          _logger.info('Removing user $currentUserId from slot $i before joining slot ${event.slotIndex}');
          updatedSlots[i] = updatedSlots[i].copyWith(playerId: null);
        }
      }

      // Then, assign the user to the target slot
      updatedSlots[event.slotIndex] = updatedSlots[event.slotIndex].copyWith(
        playerId: currentUserId,
      );

      // Check if this is a network match and update the server
      final isNetworkMatch = _isNetworkMatch(selectedMatch, state);

      if (isNetworkMatch) {
        // Check if we can access the server repository
        bool canUpdateServer = false;
        try {
          GetIt.I<ServerRepository>(); // Check if available
          canUpdateServer = true;
        } catch (e) {
          _logger.warn('ServerRepository not available for slot join: $e');
        }

        if (canUpdateServer) {
          try {
            // Use the join match API which triggers automatic removal from other matches
            final success = await _joinMatchById(selectedMatch.id, playerId: currentUserId);

            if (success) {
              _logger.info('✅ JOIN SUCCESS: Joined match ${selectedMatch.id} via slot selection, waiting for WebSocket update...');
              emit(state.setLoaded());
            } else {
              _logger.error('❌ JOIN FAILED: Could not join match ${selectedMatch.id} via slot selection');
              emit(state.setError('Failed to join match'));
            }
          } catch (e) {
            _logger.error('Error joining match via slot selection: $e');
            emit(state.setError('Error joining match: $e'));
          }
        } else {
          emit(state.setError('Server not available for slot joining'));
        }
      } else {
        // For local matches, just update local state
        final updatedMatch = selectedMatch.copyWith(playerSlots: updatedSlots);
        emit(state.copyWith(
          selectedMatch: updatedMatch,
          processingStatus: ProcessingStatus.loaded,
        ));
        _logger.info('User $currentUserId joined slot ${event.slotIndex} in local match');
      }
    } catch (e) {
      _logger.error('Error joining selected match slot: $e');
      emit(state.setError('Error joining slot: $e'));
    }
  }

  // ========================================================================
  // MATCH LIFECYCLE EVENT HANDLERS
  // ========================================================================

  Future<void> _onCreateAndStartMatch(
    CreateAndStartMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = CreateMatchRequest(
      gameConfig: state.selectedConfig!,
      playerSlots: state.playerSlots,
      gameName: state.gameName ?? '',
      openForJoining: event.openForJoining,
    );

    final result = await _matchManagementUseCase.createMatch(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (match) {
        emit(state.copyWith(
          matchId: match.id,
          selectedMatch: match,
          processingStatus: ProcessingStatus.loaded,
        ));
        add(const LoadMatchDataEvent());
      },
    );
  }

  Future<void> _onDeleteMatch(
    DeleteMatchEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    emit(state.setLoading());

    final request = DeleteMatchRequest(matchId: event.matchId);
    final result = await _matchManagementUseCase.deleteMatch(request);

    result.fold(
      (error) => emit(state.setError(error)),
      (success) {
        _logger.info('Successfully deleted match: ${event.matchId}');

        // Clear selected match if it was deleted
        if (state.selectedMatch?.id == event.matchId) {
          emit(state.copyWith(selectedMatch: null));
        }

        // Refresh matches
        add(const LoadMatchDataEvent());
        emit(state.copyWith(processingStatus: ProcessingStatus.loaded));
      },
    );
  }

  // ========================================================================
  // REAL-TIME MONITORING EVENT HANDLERS
  // ========================================================================

  Future<void> _onSubscribeToMatchUpdates(
    SubscribeToMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final request = SubscribeToRealTimeUpdatesRequest();
    final result = await _matchDataSynchronizationUseCase.subscribeToRealTimeUpdates(request);

    result.fold(
      (error) {
        _logger.error('Error subscribing to match updates: $error');
        emit(state.setError('Error subscribing to updates: $error'));
      },
      (subscriptionResult) {
        emit(state.copyWith(
          isSubscribedToUpdates: true,
          monitoredSources: state.availableMatchSources.toSet(),
        ));
        _logger.info('Successfully subscribed to real-time match updates');

        // Also set up BLoC-level WebSocket listener
        _setupWebSocketListener();
      },
    );
  }

  Future<void> _onUnsubscribeFromMatchUpdates(
    UnsubscribeFromMatchUpdatesEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final request = UnsubscribeFromRealTimeUpdatesRequest();
    final result = await _matchDataSynchronizationUseCase.unsubscribeFromRealTimeUpdates(request);

    result.fold(
      (error) {
        _logger.error('Error unsubscribing from match updates: $error');
      },
      (unsubscribeResult) {
        // Cancel BLoC WebSocket subscription
        _webSocketSubscription?.cancel();
        _webSocketSubscription = null;

        emit(state.copyWith(
          isSubscribedToUpdates: false,
          monitoredSources: {},
        ));
        _logger.info('Successfully unsubscribed from real-time match updates');
      },
    );
  }

  // ========================================================================
  // SOURCE MANAGEMENT EVENT HANDLERS
  // ========================================================================

  void _onAddMatchSource(
    AddMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final updatedSources = [...state.availableMatchSources];
    if (!updatedSources.contains(event.sourceName)) {
      updatedSources.add(event.sourceName);
      emit(state.copyWith(availableMatchSources: updatedSources));
      _logger.info('Added match source: ${event.sourceName}');
    }
  }

  Future<void> _onRemoveMatchSource(
    RemoveMatchSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) async {
    final sourceName = event.sourceName;
    _logger.info('Removing match source: $sourceName');

    final request = RemoveMatchSourceRequest(sourceName: sourceName);
    final result = await _matchSourceManagementUseCase.removeMatchSource(request);

    result.fold(
      (error) {
        _logger.error('Failed to remove match source: $error');
        emit(state.setError(error));
      },
      (removeResult) {
        // Update available sources
        final updatedSources = [...state.availableMatchSources];
        updatedSources.remove(sourceName);

        // Remove matches from this source
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource.remove(sourceName);

        // Check current network capability
        final hasNetworkCapability = _matchSourceManagementUseCase.hasNetworkCapability();

        // Update state
        emit(state.copyWith(
          availableMatchSources: updatedSources,
          matchesBySource: updatedMatchesBySource,
          hasNetworkCapability: hasNetworkCapability,
        ));

        _logger.info('Removed match source: $sourceName, remaining sources: $updatedSources');

        // If we lost network capability, disconnect from server
        if (!hasNetworkCapability) {
          _disconnectFromServer();
          _unsubscribeFromWebSocketUpdates();
        }
      },
    );
  }

  // ========================================================================
  // NETWORK & CAPABILITY EVENT HANDLERS
  // ========================================================================

  void _onNetworkCapabilityChanged(
    NetworkCapabilityChangedEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    // Check if we're losing network capability
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!event.hasNetworkCapability && state.hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to network capability loss');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to network capability loss');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        hasNetworkCapability: event.hasNetworkCapability,
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(hasNetworkCapability: event.hasNetworkCapability));
    }

    _logger.info('Network capability changed: ${event.hasNetworkCapability}');
  }

  // ========================================================================
  // UI STATE EVENT HANDLERS
  // ========================================================================

  void _onToggleServerProfileSelector(
    ToggleServerProfileSelectorEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final expanded = event.expanded ?? !state.serverProfileSelectorExpanded;
    emit(state.copyWith(serverProfileSelectorExpanded: expanded));
  }

  // ========================================================================
  // HELPER METHODS
  // ========================================================================

  /// Join a match by ID using the appropriate repository
  Future<bool> _joinMatchById(String matchId, {String? playerId}) async {
    // Try to find the match in our current state to determine source
    GameMatch? targetMatch;
    String? sourceName;

    for (final entry in state.matchesBySource.entries) {
      try {
        final match = entry.value.firstWhere((m) => m.id == matchId);
        targetMatch = match;
        sourceName = entry.key;
        break;
      } catch (e) {
        // Match not found in this source, continue to next
        continue;
      }
    }

    if (targetMatch == null || sourceName == null) {
      _logger.warn('Match $matchId not found in any source');
      return false;
    }

    final repository = _matchRepositories[sourceName];
    if (repository == null) {
      _logger.error('Repository not found for source: $sourceName');
      return false;
    }

    return await repository.joinMatch(matchId, playerId: playerId);
  }

  /// Leave a match by ID using the appropriate repository
  Future<bool> _leaveMatchById(String matchId, {String? playerId}) async {
    // Try all repositories since we might not know which one has the match
    // Create a snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    for (final repository in repositoriesSnapshot.values) {
      try {
        final success = await repository.leaveMatch(matchId, playerId: playerId);
        if (success) {
          return true;
        }
      } catch (e) {
        _logger.warn('Failed to leave match $matchId from repository: $e');
      }
    }

    return false;
  }

  /// Handle match updates from a specific source
  void _onHandleMatchUpdatesFromSource(
    HandleMatchUpdatesFromSourceEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
    updatedMatchesBySource[event.sourceName] = event.matches;

    // Rebuild the complete matches list
    final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

    emit(state.copyWith(
      openMatches: allMatches,
      matchesBySource: updatedMatchesBySource,
    ));

    _logger.info('Received ${event.matches.length} match updates from ${event.sourceName}');
  }

  // ========================================================================
  // NETWORK SOURCE INTEGRATION
  // ========================================================================



  /// Add a network match source when server connection is established
  /// @deprecated This method is deprecated as old use cases have been removed.
  /// Network repositories are now added automatically via DI.
  @Deprecated('Network repositories are now added automatically via DI')
  void addNetworkSource(MatchSelectionUseCase networkUseCase) {
    _logger.warn('addNetworkSource called with deprecated use case. Network repositories should be registered via DI.');
  }

  /// Re-initialize repositories when server scope becomes available
  /// This should be called when the server connection is established
  void _onServerScopeAvailable(
    ServerScopeAvailableEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Server scope became available, re-initializing repositories');
    _logger.info('Current GetIt scope: ${GetIt.I.currentScopeName}');

    // Re-initialize repositories to pick up network sources
    _initializeRepositories();

    // Update state with new sources
    final updatedSources = _matchRepositories.keys.toList();
    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    emit(state.copyWith(
      availableMatchSources: updatedSources,
      hasNetworkCapability: hasNetworkCapability,
    ));

    _logger.info('Updated match sources after server scope activation: $updatedSources');

    // Subscribe to WebSocket updates when network becomes available
    if (hasNetworkCapability) {
      add(const SubscribeToMatchUpdatesEvent());
    }

    // Load matches from newly available sources
    add(const LoadMatchDataEvent());
  }

  /// Public method to trigger server scope available event
  void onServerScopeAvailable() {
    add(const ServerScopeAvailableEvent());
  }

  /// Clean up when server scope is lost
  /// This should be called when the server connection is lost
  void _onServerScopeLost(
    ServerScopeLostEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    _logger.info('Server scope lost, cleaning up network resources');
    _logger.info('Current GetIt scope: ${GetIt.I.currentScopeName}');

    // Remove network repository
    _matchRepositories.remove('network');

    // Update state
    final updatedSources = _matchRepositories.keys.toList();
    // Create snapshot to avoid concurrent modification during iteration
    final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
    final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

    // Check if we're currently creating a network match and clear it
    bool shouldClearMatchCreation = false;
    bool shouldClearSelectedMatch = false;

    if (!hasNetworkCapability) {
      // Check if currently creating a network match
      if (state.isCreatingMatch) {
        final hasNetworkPlayers = state.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (hasNetworkPlayers) {
          _logger.info('Clearing network match creation due to server scope loss');
          shouldClearMatchCreation = true;
        }
      }

      // Check if we have a selected network match for joining
      if (state.selectedMatch != null) {
        final selectedMatchHasNetworkPlayers = state.selectedMatch!.playerSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

        if (selectedMatchHasNetworkPlayers) {
          _logger.info('Clearing selected network match due to server scope loss');
          shouldClearSelectedMatch = true;
        }
      }
    }

    if (shouldClearMatchCreation) {
      // Clear match creation and return to selection mode
      emit(state.enterSelectionMode().copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        selectedConfigId: null,
        gameName: null,
        matchId: null,
        playerSlots: [],
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: shouldClearSelectedMatch ? null : state.selectedMatch,
      ));
    } else if (shouldClearSelectedMatch) {
      // Just clear the selected match
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
        selectedMatch: null,
      ));
    } else {
      emit(state.copyWith(
        availableMatchSources: updatedSources,
        hasNetworkCapability: hasNetworkCapability,
        matchesBySource: Map<String, List<GameMatch>>.from(state.matchesBySource)..remove('network'),
      ));
    }

    // Unsubscribe from WebSocket updates
    _unsubscribeFromWebSocketUpdates();

    _logger.info('Server scope cleanup completed, remaining sources: $updatedSources');
  }

  /// Public method to trigger server scope lost event
  void onServerScopeLost() {
    add(const ServerScopeLostEvent());
  }

  /// Remove a network match source when server connection is lost
  void removeNetworkSource(String sourceName) {
    // Use the existing RemoveMatchSourceEvent to handle this properly
    add(RemoveMatchSourceEvent(sourceName));
  }

  /// Disconnect from server and clean up network resources
  void _disconnectFromServer() {
    try {
      _logger.info('Disconnecting from server');

      // Notify ServerEnvironmentManager to disconnect by setting profile to null
      try {
        GetIt.I<ServerEnvironmentManager>().add(SetSelectedServerProfileEvent(null));
      } catch (e) {
        _logger.warn('ServerEnvironmentManager not available for disconnect: $e');
      }

      _logger.info('Server disconnection completed');
    } catch (e) {
      _logger.error('Error during server disconnection: $e');
    }
  }

  // ========================================================================
  // WEBSOCKET INTEGRATION
  // ========================================================================



  /// Unsubscribe from WebSocket updates
  void _unsubscribeFromWebSocketUpdates() {
    try {
      _openMatchesSubscription?.cancel();
      _openMatchesSubscription = null;

      // Unsubscribe from server notifications
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();
      serverNotificationsUseCase.unsubscribeFromOpenMatches();

      _logger.info('Unsubscribed from WebSocket updates');
    } catch (e) {
      _logger.error('Failed to unsubscribe from WebSocket updates: $e');
    }
  }

  /// Set up WebSocket listener for the BLoC
  void _setupWebSocketListener() {
    try {
      // Cancel any existing subscription first
      _webSocketSubscription?.cancel();

      // Get the server notifications use case
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();

      // Listen for open matches updates from the WebSocket and dispatch BLoC events
      _webSocketSubscription = serverNotificationsUseCase.openMatchesUpdates.listen(
        (matches) {
          _logger.info('🔄 BLoC WEBSOCKET: Received ${matches.length} matches for BLoC processing');
          // Dispatch the event to update the BLoC state
          add(HandleWebSocketMatchUpdateEvent(matches));
        },
        onError: (error) {
          _logger.error('Error in BLoC WebSocket listener: $error');
        },
        onDone: () {
          _logger.info('BLoC WebSocket listener stream closed');
          _webSocketSubscription = null;
        },
      );

      _logger.info('BLoC WebSocket listener set up successfully');
    } catch (e) {
      _logger.error('Failed to set up BLoC WebSocket listener: $e');
    }
  }

  /// Handle WebSocket match updates
  void _onHandleWebSocketMatchUpdate(
    HandleWebSocketMatchUpdateEvent event,
    Emitter<MatchManagementState> emit,
  ) {
    try {
      // Update the network source matches
      // Create a snapshot to avoid concurrent modification during iteration
      final repositoriesSnapshot = Map<String, MatchRepositoryInterface>.from(_matchRepositories);
      final networkSourceName = repositoriesSnapshot.entries
          .where((entry) => entry.value.supportsRealTimeUpdates)
          .map((entry) => entry.key)
          .firstOrNull;

      if (networkSourceName != null) {
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(state.matchesBySource);
        updatedMatchesBySource[networkSourceName] = event.matches;

        // Rebuild the complete matches list
        final allMatches = updatedMatchesBySource.values.expand((list) => list).toList();

        // Check if selectedMatch needs to be updated
        GameMatch? updatedSelectedMatch = state.selectedMatch;
        if (state.selectedMatch != null) {
          // Find the updated version of the selected match
          final matchingMatch = event.matches.firstWhere(
            (match) => match.id == state.selectedMatch!.id,
            orElse: () => state.selectedMatch!,
          );
          if (matchingMatch.id == state.selectedMatch!.id) {
            updatedSelectedMatch = matchingMatch;
            _logger.info('🔄 WEBSOCKET: Updated selectedMatch ${matchingMatch.id}');
          }
        }

        emit(state.copyWith(
          openMatches: allMatches,
          matchesBySource: updatedMatchesBySource,
          selectedMatch: updatedSelectedMatch,
        ));

        _logger.info('🔄 WEBSOCKET APPLIED: Updated ${event.matches.length} matches from $networkSourceName');
        _logger.info('📊 TOTAL MATCHES: ${allMatches.length} matches now available');
      } else {
        _logger.warn('No network source found for WebSocket update');
      }
    } catch (e) {
      _logger.error('Failed to handle WebSocket match update: $e');
    }
  }

  // ========================================================================
  // NETWORK MATCH CREATION HELPERS
  // ========================================================================





  /// Check if a source is a local source
  bool _isLocalSource(String sourceName) {
    return sourceName.toLowerCase().contains('local') ||
           sourceName.toLowerCase().contains('file');
  }

  /// Check if a match is from a network source (server match)
  bool _isNetworkMatch(GameMatch match, MatchManagementState currentState) {
    // Check if the match exists in any network source
    for (final entry in currentState.matchesBySource.entries) {
      final sourceName = entry.key;
      final matches = entry.value;

      // Network sources typically have names like "Network", "Server", etc.
      if (sourceName.toLowerCase().contains('network') ||
          sourceName.toLowerCase().contains('server')) {
        if (matches.any((m) => m.id == match.id)) {
          _logger.info('Match ${match.id} identified as network match from source: $sourceName');
          return true;
        }
      }
    }
    return false;
  }

  // ========================================================================
  // CLEANUP
  // ========================================================================

  @override
  Future<void> close() async {
    // Cancel all stream subscriptions
    for (final subscription in _sourceSubscriptions.values) {
      await subscription.cancel();
    }
    _sourceSubscriptions.clear();

    // Cancel WebSocket subscription
    _unsubscribeFromWebSocketUpdates();

    _logger.info('MatchManagementBloc closed');
    return super.close();
  }
}


