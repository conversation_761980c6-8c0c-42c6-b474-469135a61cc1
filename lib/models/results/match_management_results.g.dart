// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_management_results.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchDataResult _$MatchDataResultFromJson(Map<String, dynamic> json) =>
    _MatchDataResult(
      allMatches: (json['allMatches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      matchesBySource: (json['matchesBySource'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      availableSources: (json['availableSources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchDataResultToJson(_MatchDataResult instance) =>
    <String, dynamic>{
      'allMatches': instance.allMatches,
      'matchesBySource': instance.matchesBySource,
      'availableSources': instance.availableSources,
      'hasNetworkCapability': instance.hasNetworkCapability,
    };

_PlayerSlotUpdateResult _$PlayerSlotUpdateResultFromJson(
        Map<String, dynamic> json) =>
    _PlayerSlotUpdateResult(
      updatedSlots: (json['updatedSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      requiresNetworkMatch: json['requiresNetworkMatch'] as bool,
      repositoryType: json['repositoryType'] as String?,
      updatedMatch: json['updatedMatch'] == null
          ? null
          : GameMatch.fromJson(json['updatedMatch'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlayerSlotUpdateResultToJson(
        _PlayerSlotUpdateResult instance) =>
    <String, dynamic>{
      'updatedSlots': instance.updatedSlots,
      'requiresNetworkMatch': instance.requiresNetworkMatch,
      'repositoryType': instance.repositoryType,
      'updatedMatch': instance.updatedMatch,
    };

_RepositoryInitializationResult _$RepositoryInitializationResultFromJson(
        Map<String, dynamic> json) =>
    _RepositoryInitializationResult(
      availableSources: (json['availableSources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sourceCapabilities:
          Map<String, bool>.from(json['sourceCapabilities'] as Map),
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
    );

Map<String, dynamic> _$RepositoryInitializationResultToJson(
        _RepositoryInitializationResult instance) =>
    <String, dynamic>{
      'availableSources': instance.availableSources,
      'sourceCapabilities': instance.sourceCapabilities,
      'hasNetworkCapability': instance.hasNetworkCapability,
    };

_SourceRefreshResult _$SourceRefreshResultFromJson(Map<String, dynamic> json) =>
    _SourceRefreshResult(
      sourceName: json['sourceName'] as String,
      matches: (json['matches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      isNetworkSource: json['isNetworkSource'] as bool? ?? false,
    );

Map<String, dynamic> _$SourceRefreshResultToJson(
        _SourceRefreshResult instance) =>
    <String, dynamic>{
      'sourceName': instance.sourceName,
      'matches': instance.matches,
      'isNetworkSource': instance.isNetworkSource,
    };

_MatchUpdateResult _$MatchUpdateResultFromJson(Map<String, dynamic> json) =>
    _MatchUpdateResult(
      updatedMatches: (json['updatedMatches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      sourceName: json['sourceName'] as String,
      requiresStateUpdate: json['requiresStateUpdate'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchUpdateResultToJson(_MatchUpdateResult instance) =>
    <String, dynamic>{
      'updatedMatches': instance.updatedMatches,
      'sourceName': instance.sourceName,
      'requiresStateUpdate': instance.requiresStateUpdate,
    };

_SubscriptionResult _$SubscriptionResultFromJson(Map<String, dynamic> json) =>
    _SubscriptionResult(
      subscribed: json['subscribed'] as bool? ?? false,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SubscriptionResultToJson(_SubscriptionResult instance) =>
    <String, dynamic>{
      'subscribed': instance.subscribed,
      'message': instance.message,
    };

_UnsubscriptionResult _$UnsubscriptionResultFromJson(
        Map<String, dynamic> json) =>
    _UnsubscriptionResult(
      unsubscribed: json['unsubscribed'] as bool? ?? false,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$UnsubscriptionResultToJson(
        _UnsubscriptionResult instance) =>
    <String, dynamic>{
      'unsubscribed': instance.unsubscribed,
      'message': instance.message,
    };

_SourceRemovalResult _$SourceRemovalResultFromJson(Map<String, dynamic> json) =>
    _SourceRemovalResult(
      sourceRemoved: json['sourceRemoved'] as bool? ?? false,
      removedSourceName: json['removedSourceName'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SourceRemovalResultToJson(
        _SourceRemovalResult instance) =>
    <String, dynamic>{
      'sourceRemoved': instance.sourceRemoved,
      'removedSourceName': instance.removedSourceName,
      'message': instance.message,
    };
