// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_management_results.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchDataResult {
  List<GameMatch> get allMatches;
  Map<String, List<GameMatch>> get matchesBySource;
  List<String> get availableSources;
  bool get hasNetworkCapability;

  /// Create a copy of MatchDataResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchDataResultCopyWith<MatchDataResult> get copyWith =>
      _$MatchDataResultCopyWithImpl<MatchDataResult>(
          this as MatchDataResult, _$identity);

  /// Serializes this MatchDataResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchDataResult &&
            const DeepCollectionEquality()
                .equals(other.allMatches, allMatches) &&
            const DeepCollectionEquality()
                .equals(other.matchesBySource, matchesBySource) &&
            const DeepCollectionEquality()
                .equals(other.availableSources, availableSources) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(allMatches),
      const DeepCollectionEquality().hash(matchesBySource),
      const DeepCollectionEquality().hash(availableSources),
      hasNetworkCapability);

  @override
  String toString() {
    return 'MatchDataResult(allMatches: $allMatches, matchesBySource: $matchesBySource, availableSources: $availableSources, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class $MatchDataResultCopyWith<$Res> {
  factory $MatchDataResultCopyWith(
          MatchDataResult value, $Res Function(MatchDataResult) _then) =
      _$MatchDataResultCopyWithImpl;
  @useResult
  $Res call(
      {List<GameMatch> allMatches,
      Map<String, List<GameMatch>> matchesBySource,
      List<String> availableSources,
      bool hasNetworkCapability});
}

/// @nodoc
class _$MatchDataResultCopyWithImpl<$Res>
    implements $MatchDataResultCopyWith<$Res> {
  _$MatchDataResultCopyWithImpl(this._self, this._then);

  final MatchDataResult _self;
  final $Res Function(MatchDataResult) _then;

  /// Create a copy of MatchDataResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allMatches = null,
    Object? matchesBySource = null,
    Object? availableSources = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_self.copyWith(
      allMatches: null == allMatches
          ? _self.allMatches
          : allMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      matchesBySource: null == matchesBySource
          ? _self.matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      availableSources: null == availableSources
          ? _self.availableSources
          : availableSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchDataResult implements MatchDataResult {
  const _MatchDataResult(
      {required final List<GameMatch> allMatches,
      required final Map<String, List<GameMatch>> matchesBySource,
      required final List<String> availableSources,
      this.hasNetworkCapability = false})
      : _allMatches = allMatches,
        _matchesBySource = matchesBySource,
        _availableSources = availableSources;
  factory _MatchDataResult.fromJson(Map<String, dynamic> json) =>
      _$MatchDataResultFromJson(json);

  final List<GameMatch> _allMatches;
  @override
  List<GameMatch> get allMatches {
    if (_allMatches is EqualUnmodifiableListView) return _allMatches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allMatches);
  }

  final Map<String, List<GameMatch>> _matchesBySource;
  @override
  Map<String, List<GameMatch>> get matchesBySource {
    if (_matchesBySource is EqualUnmodifiableMapView) return _matchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_matchesBySource);
  }

  final List<String> _availableSources;
  @override
  List<String> get availableSources {
    if (_availableSources is EqualUnmodifiableListView)
      return _availableSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableSources);
  }

  @override
  @JsonKey()
  final bool hasNetworkCapability;

  /// Create a copy of MatchDataResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchDataResultCopyWith<_MatchDataResult> get copyWith =>
      __$MatchDataResultCopyWithImpl<_MatchDataResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchDataResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchDataResult &&
            const DeepCollectionEquality()
                .equals(other._allMatches, _allMatches) &&
            const DeepCollectionEquality()
                .equals(other._matchesBySource, _matchesBySource) &&
            const DeepCollectionEquality()
                .equals(other._availableSources, _availableSources) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_allMatches),
      const DeepCollectionEquality().hash(_matchesBySource),
      const DeepCollectionEquality().hash(_availableSources),
      hasNetworkCapability);

  @override
  String toString() {
    return 'MatchDataResult(allMatches: $allMatches, matchesBySource: $matchesBySource, availableSources: $availableSources, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class _$MatchDataResultCopyWith<$Res>
    implements $MatchDataResultCopyWith<$Res> {
  factory _$MatchDataResultCopyWith(
          _MatchDataResult value, $Res Function(_MatchDataResult) _then) =
      __$MatchDataResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<GameMatch> allMatches,
      Map<String, List<GameMatch>> matchesBySource,
      List<String> availableSources,
      bool hasNetworkCapability});
}

/// @nodoc
class __$MatchDataResultCopyWithImpl<$Res>
    implements _$MatchDataResultCopyWith<$Res> {
  __$MatchDataResultCopyWithImpl(this._self, this._then);

  final _MatchDataResult _self;
  final $Res Function(_MatchDataResult) _then;

  /// Create a copy of MatchDataResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? allMatches = null,
    Object? matchesBySource = null,
    Object? availableSources = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_MatchDataResult(
      allMatches: null == allMatches
          ? _self._allMatches
          : allMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      matchesBySource: null == matchesBySource
          ? _self._matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      availableSources: null == availableSources
          ? _self._availableSources
          : availableSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$PlayerSlotUpdateResult {
  List<PlayerSlot> get updatedSlots;
  bool get requiresNetworkMatch;
  String? get repositoryType;
  GameMatch? get updatedMatch;

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PlayerSlotUpdateResultCopyWith<PlayerSlotUpdateResult> get copyWith =>
      _$PlayerSlotUpdateResultCopyWithImpl<PlayerSlotUpdateResult>(
          this as PlayerSlotUpdateResult, _$identity);

  /// Serializes this PlayerSlotUpdateResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PlayerSlotUpdateResult &&
            const DeepCollectionEquality()
                .equals(other.updatedSlots, updatedSlots) &&
            (identical(other.requiresNetworkMatch, requiresNetworkMatch) ||
                other.requiresNetworkMatch == requiresNetworkMatch) &&
            (identical(other.repositoryType, repositoryType) ||
                other.repositoryType == repositoryType) &&
            (identical(other.updatedMatch, updatedMatch) ||
                other.updatedMatch == updatedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(updatedSlots),
      requiresNetworkMatch,
      repositoryType,
      updatedMatch);

  @override
  String toString() {
    return 'PlayerSlotUpdateResult(updatedSlots: $updatedSlots, requiresNetworkMatch: $requiresNetworkMatch, repositoryType: $repositoryType, updatedMatch: $updatedMatch)';
  }
}

/// @nodoc
abstract mixin class $PlayerSlotUpdateResultCopyWith<$Res> {
  factory $PlayerSlotUpdateResultCopyWith(PlayerSlotUpdateResult value,
          $Res Function(PlayerSlotUpdateResult) _then) =
      _$PlayerSlotUpdateResultCopyWithImpl;
  @useResult
  $Res call(
      {List<PlayerSlot> updatedSlots,
      bool requiresNetworkMatch,
      String? repositoryType,
      GameMatch? updatedMatch});

  $GameMatchCopyWith<$Res>? get updatedMatch;
}

/// @nodoc
class _$PlayerSlotUpdateResultCopyWithImpl<$Res>
    implements $PlayerSlotUpdateResultCopyWith<$Res> {
  _$PlayerSlotUpdateResultCopyWithImpl(this._self, this._then);

  final PlayerSlotUpdateResult _self;
  final $Res Function(PlayerSlotUpdateResult) _then;

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updatedSlots = null,
    Object? requiresNetworkMatch = null,
    Object? repositoryType = freezed,
    Object? updatedMatch = freezed,
  }) {
    return _then(_self.copyWith(
      updatedSlots: null == updatedSlots
          ? _self.updatedSlots
          : updatedSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      requiresNetworkMatch: null == requiresNetworkMatch
          ? _self.requiresNetworkMatch
          : requiresNetworkMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      repositoryType: freezed == repositoryType
          ? _self.repositoryType
          : repositoryType // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedMatch: freezed == updatedMatch
          ? _self.updatedMatch
          : updatedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get updatedMatch {
    if (_self.updatedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.updatedMatch!, (value) {
      return _then(_self.copyWith(updatedMatch: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _PlayerSlotUpdateResult implements PlayerSlotUpdateResult {
  const _PlayerSlotUpdateResult(
      {required final List<PlayerSlot> updatedSlots,
      required this.requiresNetworkMatch,
      this.repositoryType,
      this.updatedMatch})
      : _updatedSlots = updatedSlots;
  factory _PlayerSlotUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$PlayerSlotUpdateResultFromJson(json);

  final List<PlayerSlot> _updatedSlots;
  @override
  List<PlayerSlot> get updatedSlots {
    if (_updatedSlots is EqualUnmodifiableListView) return _updatedSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_updatedSlots);
  }

  @override
  final bool requiresNetworkMatch;
  @override
  final String? repositoryType;
  @override
  final GameMatch? updatedMatch;

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PlayerSlotUpdateResultCopyWith<_PlayerSlotUpdateResult> get copyWith =>
      __$PlayerSlotUpdateResultCopyWithImpl<_PlayerSlotUpdateResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PlayerSlotUpdateResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PlayerSlotUpdateResult &&
            const DeepCollectionEquality()
                .equals(other._updatedSlots, _updatedSlots) &&
            (identical(other.requiresNetworkMatch, requiresNetworkMatch) ||
                other.requiresNetworkMatch == requiresNetworkMatch) &&
            (identical(other.repositoryType, repositoryType) ||
                other.repositoryType == repositoryType) &&
            (identical(other.updatedMatch, updatedMatch) ||
                other.updatedMatch == updatedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_updatedSlots),
      requiresNetworkMatch,
      repositoryType,
      updatedMatch);

  @override
  String toString() {
    return 'PlayerSlotUpdateResult(updatedSlots: $updatedSlots, requiresNetworkMatch: $requiresNetworkMatch, repositoryType: $repositoryType, updatedMatch: $updatedMatch)';
  }
}

/// @nodoc
abstract mixin class _$PlayerSlotUpdateResultCopyWith<$Res>
    implements $PlayerSlotUpdateResultCopyWith<$Res> {
  factory _$PlayerSlotUpdateResultCopyWith(_PlayerSlotUpdateResult value,
          $Res Function(_PlayerSlotUpdateResult) _then) =
      __$PlayerSlotUpdateResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<PlayerSlot> updatedSlots,
      bool requiresNetworkMatch,
      String? repositoryType,
      GameMatch? updatedMatch});

  @override
  $GameMatchCopyWith<$Res>? get updatedMatch;
}

/// @nodoc
class __$PlayerSlotUpdateResultCopyWithImpl<$Res>
    implements _$PlayerSlotUpdateResultCopyWith<$Res> {
  __$PlayerSlotUpdateResultCopyWithImpl(this._self, this._then);

  final _PlayerSlotUpdateResult _self;
  final $Res Function(_PlayerSlotUpdateResult) _then;

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? updatedSlots = null,
    Object? requiresNetworkMatch = null,
    Object? repositoryType = freezed,
    Object? updatedMatch = freezed,
  }) {
    return _then(_PlayerSlotUpdateResult(
      updatedSlots: null == updatedSlots
          ? _self._updatedSlots
          : updatedSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      requiresNetworkMatch: null == requiresNetworkMatch
          ? _self.requiresNetworkMatch
          : requiresNetworkMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      repositoryType: freezed == repositoryType
          ? _self.repositoryType
          : repositoryType // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedMatch: freezed == updatedMatch
          ? _self.updatedMatch
          : updatedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of PlayerSlotUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get updatedMatch {
    if (_self.updatedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.updatedMatch!, (value) {
      return _then(_self.copyWith(updatedMatch: value));
    });
  }
}

/// @nodoc
mixin _$RepositoryInitializationResult {
  List<String> get availableSources;
  Map<String, bool> get sourceCapabilities;
  bool get hasNetworkCapability;

  /// Create a copy of RepositoryInitializationResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RepositoryInitializationResultCopyWith<RepositoryInitializationResult>
      get copyWith => _$RepositoryInitializationResultCopyWithImpl<
              RepositoryInitializationResult>(
          this as RepositoryInitializationResult, _$identity);

  /// Serializes this RepositoryInitializationResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RepositoryInitializationResult &&
            const DeepCollectionEquality()
                .equals(other.availableSources, availableSources) &&
            const DeepCollectionEquality()
                .equals(other.sourceCapabilities, sourceCapabilities) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(availableSources),
      const DeepCollectionEquality().hash(sourceCapabilities),
      hasNetworkCapability);

  @override
  String toString() {
    return 'RepositoryInitializationResult(availableSources: $availableSources, sourceCapabilities: $sourceCapabilities, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class $RepositoryInitializationResultCopyWith<$Res> {
  factory $RepositoryInitializationResultCopyWith(
          RepositoryInitializationResult value,
          $Res Function(RepositoryInitializationResult) _then) =
      _$RepositoryInitializationResultCopyWithImpl;
  @useResult
  $Res call(
      {List<String> availableSources,
      Map<String, bool> sourceCapabilities,
      bool hasNetworkCapability});
}

/// @nodoc
class _$RepositoryInitializationResultCopyWithImpl<$Res>
    implements $RepositoryInitializationResultCopyWith<$Res> {
  _$RepositoryInitializationResultCopyWithImpl(this._self, this._then);

  final RepositoryInitializationResult _self;
  final $Res Function(RepositoryInitializationResult) _then;

  /// Create a copy of RepositoryInitializationResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableSources = null,
    Object? sourceCapabilities = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_self.copyWith(
      availableSources: null == availableSources
          ? _self.availableSources
          : availableSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sourceCapabilities: null == sourceCapabilities
          ? _self.sourceCapabilities
          : sourceCapabilities // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RepositoryInitializationResult
    implements RepositoryInitializationResult {
  const _RepositoryInitializationResult(
      {required final List<String> availableSources,
      required final Map<String, bool> sourceCapabilities,
      this.hasNetworkCapability = false})
      : _availableSources = availableSources,
        _sourceCapabilities = sourceCapabilities;
  factory _RepositoryInitializationResult.fromJson(Map<String, dynamic> json) =>
      _$RepositoryInitializationResultFromJson(json);

  final List<String> _availableSources;
  @override
  List<String> get availableSources {
    if (_availableSources is EqualUnmodifiableListView)
      return _availableSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableSources);
  }

  final Map<String, bool> _sourceCapabilities;
  @override
  Map<String, bool> get sourceCapabilities {
    if (_sourceCapabilities is EqualUnmodifiableMapView)
      return _sourceCapabilities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_sourceCapabilities);
  }

  @override
  @JsonKey()
  final bool hasNetworkCapability;

  /// Create a copy of RepositoryInitializationResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RepositoryInitializationResultCopyWith<_RepositoryInitializationResult>
      get copyWith => __$RepositoryInitializationResultCopyWithImpl<
          _RepositoryInitializationResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RepositoryInitializationResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RepositoryInitializationResult &&
            const DeepCollectionEquality()
                .equals(other._availableSources, _availableSources) &&
            const DeepCollectionEquality()
                .equals(other._sourceCapabilities, _sourceCapabilities) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_availableSources),
      const DeepCollectionEquality().hash(_sourceCapabilities),
      hasNetworkCapability);

  @override
  String toString() {
    return 'RepositoryInitializationResult(availableSources: $availableSources, sourceCapabilities: $sourceCapabilities, hasNetworkCapability: $hasNetworkCapability)';
  }
}

/// @nodoc
abstract mixin class _$RepositoryInitializationResultCopyWith<$Res>
    implements $RepositoryInitializationResultCopyWith<$Res> {
  factory _$RepositoryInitializationResultCopyWith(
          _RepositoryInitializationResult value,
          $Res Function(_RepositoryInitializationResult) _then) =
      __$RepositoryInitializationResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<String> availableSources,
      Map<String, bool> sourceCapabilities,
      bool hasNetworkCapability});
}

/// @nodoc
class __$RepositoryInitializationResultCopyWithImpl<$Res>
    implements _$RepositoryInitializationResultCopyWith<$Res> {
  __$RepositoryInitializationResultCopyWithImpl(this._self, this._then);

  final _RepositoryInitializationResult _self;
  final $Res Function(_RepositoryInitializationResult) _then;

  /// Create a copy of RepositoryInitializationResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? availableSources = null,
    Object? sourceCapabilities = null,
    Object? hasNetworkCapability = null,
  }) {
    return _then(_RepositoryInitializationResult(
      availableSources: null == availableSources
          ? _self._availableSources
          : availableSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sourceCapabilities: null == sourceCapabilities
          ? _self._sourceCapabilities
          : sourceCapabilities // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$SourceRefreshResult {
  String get sourceName;
  List<GameMatch> get matches;
  bool get isNetworkSource;

  /// Create a copy of SourceRefreshResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SourceRefreshResultCopyWith<SourceRefreshResult> get copyWith =>
      _$SourceRefreshResultCopyWithImpl<SourceRefreshResult>(
          this as SourceRefreshResult, _$identity);

  /// Serializes this SourceRefreshResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SourceRefreshResult &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            const DeepCollectionEquality().equals(other.matches, matches) &&
            (identical(other.isNetworkSource, isNetworkSource) ||
                other.isNetworkSource == isNetworkSource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName,
      const DeepCollectionEquality().hash(matches), isNetworkSource);

  @override
  String toString() {
    return 'SourceRefreshResult(sourceName: $sourceName, matches: $matches, isNetworkSource: $isNetworkSource)';
  }
}

/// @nodoc
abstract mixin class $SourceRefreshResultCopyWith<$Res> {
  factory $SourceRefreshResultCopyWith(
          SourceRefreshResult value, $Res Function(SourceRefreshResult) _then) =
      _$SourceRefreshResultCopyWithImpl;
  @useResult
  $Res call({String sourceName, List<GameMatch> matches, bool isNetworkSource});
}

/// @nodoc
class _$SourceRefreshResultCopyWithImpl<$Res>
    implements $SourceRefreshResultCopyWith<$Res> {
  _$SourceRefreshResultCopyWithImpl(this._self, this._then);

  final SourceRefreshResult _self;
  final $Res Function(SourceRefreshResult) _then;

  /// Create a copy of SourceRefreshResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
    Object? matches = null,
    Object? isNetworkSource = null,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      matches: null == matches
          ? _self.matches
          : matches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      isNetworkSource: null == isNetworkSource
          ? _self.isNetworkSource
          : isNetworkSource // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SourceRefreshResult implements SourceRefreshResult {
  const _SourceRefreshResult(
      {required this.sourceName,
      required final List<GameMatch> matches,
      this.isNetworkSource = false})
      : _matches = matches;
  factory _SourceRefreshResult.fromJson(Map<String, dynamic> json) =>
      _$SourceRefreshResultFromJson(json);

  @override
  final String sourceName;
  final List<GameMatch> _matches;
  @override
  List<GameMatch> get matches {
    if (_matches is EqualUnmodifiableListView) return _matches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_matches);
  }

  @override
  @JsonKey()
  final bool isNetworkSource;

  /// Create a copy of SourceRefreshResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SourceRefreshResultCopyWith<_SourceRefreshResult> get copyWith =>
      __$SourceRefreshResultCopyWithImpl<_SourceRefreshResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SourceRefreshResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SourceRefreshResult &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            const DeepCollectionEquality().equals(other._matches, _matches) &&
            (identical(other.isNetworkSource, isNetworkSource) ||
                other.isNetworkSource == isNetworkSource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName,
      const DeepCollectionEquality().hash(_matches), isNetworkSource);

  @override
  String toString() {
    return 'SourceRefreshResult(sourceName: $sourceName, matches: $matches, isNetworkSource: $isNetworkSource)';
  }
}

/// @nodoc
abstract mixin class _$SourceRefreshResultCopyWith<$Res>
    implements $SourceRefreshResultCopyWith<$Res> {
  factory _$SourceRefreshResultCopyWith(_SourceRefreshResult value,
          $Res Function(_SourceRefreshResult) _then) =
      __$SourceRefreshResultCopyWithImpl;
  @override
  @useResult
  $Res call({String sourceName, List<GameMatch> matches, bool isNetworkSource});
}

/// @nodoc
class __$SourceRefreshResultCopyWithImpl<$Res>
    implements _$SourceRefreshResultCopyWith<$Res> {
  __$SourceRefreshResultCopyWithImpl(this._self, this._then);

  final _SourceRefreshResult _self;
  final $Res Function(_SourceRefreshResult) _then;

  /// Create a copy of SourceRefreshResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
    Object? matches = null,
    Object? isNetworkSource = null,
  }) {
    return _then(_SourceRefreshResult(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      matches: null == matches
          ? _self._matches
          : matches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      isNetworkSource: null == isNetworkSource
          ? _self.isNetworkSource
          : isNetworkSource // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$MatchUpdateResult {
  List<GameMatch> get updatedMatches;
  String get sourceName;
  bool get requiresStateUpdate;

  /// Create a copy of MatchUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchUpdateResultCopyWith<MatchUpdateResult> get copyWith =>
      _$MatchUpdateResultCopyWithImpl<MatchUpdateResult>(
          this as MatchUpdateResult, _$identity);

  /// Serializes this MatchUpdateResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchUpdateResult &&
            const DeepCollectionEquality()
                .equals(other.updatedMatches, updatedMatches) &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.requiresStateUpdate, requiresStateUpdate) ||
                other.requiresStateUpdate == requiresStateUpdate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(updatedMatches),
      sourceName,
      requiresStateUpdate);

  @override
  String toString() {
    return 'MatchUpdateResult(updatedMatches: $updatedMatches, sourceName: $sourceName, requiresStateUpdate: $requiresStateUpdate)';
  }
}

/// @nodoc
abstract mixin class $MatchUpdateResultCopyWith<$Res> {
  factory $MatchUpdateResultCopyWith(
          MatchUpdateResult value, $Res Function(MatchUpdateResult) _then) =
      _$MatchUpdateResultCopyWithImpl;
  @useResult
  $Res call(
      {List<GameMatch> updatedMatches,
      String sourceName,
      bool requiresStateUpdate});
}

/// @nodoc
class _$MatchUpdateResultCopyWithImpl<$Res>
    implements $MatchUpdateResultCopyWith<$Res> {
  _$MatchUpdateResultCopyWithImpl(this._self, this._then);

  final MatchUpdateResult _self;
  final $Res Function(MatchUpdateResult) _then;

  /// Create a copy of MatchUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updatedMatches = null,
    Object? sourceName = null,
    Object? requiresStateUpdate = null,
  }) {
    return _then(_self.copyWith(
      updatedMatches: null == updatedMatches
          ? _self.updatedMatches
          : updatedMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      requiresStateUpdate: null == requiresStateUpdate
          ? _self.requiresStateUpdate
          : requiresStateUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MatchUpdateResult implements MatchUpdateResult {
  const _MatchUpdateResult(
      {required final List<GameMatch> updatedMatches,
      required this.sourceName,
      this.requiresStateUpdate = false})
      : _updatedMatches = updatedMatches;
  factory _MatchUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$MatchUpdateResultFromJson(json);

  final List<GameMatch> _updatedMatches;
  @override
  List<GameMatch> get updatedMatches {
    if (_updatedMatches is EqualUnmodifiableListView) return _updatedMatches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_updatedMatches);
  }

  @override
  final String sourceName;
  @override
  @JsonKey()
  final bool requiresStateUpdate;

  /// Create a copy of MatchUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchUpdateResultCopyWith<_MatchUpdateResult> get copyWith =>
      __$MatchUpdateResultCopyWithImpl<_MatchUpdateResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchUpdateResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchUpdateResult &&
            const DeepCollectionEquality()
                .equals(other._updatedMatches, _updatedMatches) &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.requiresStateUpdate, requiresStateUpdate) ||
                other.requiresStateUpdate == requiresStateUpdate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_updatedMatches),
      sourceName,
      requiresStateUpdate);

  @override
  String toString() {
    return 'MatchUpdateResult(updatedMatches: $updatedMatches, sourceName: $sourceName, requiresStateUpdate: $requiresStateUpdate)';
  }
}

/// @nodoc
abstract mixin class _$MatchUpdateResultCopyWith<$Res>
    implements $MatchUpdateResultCopyWith<$Res> {
  factory _$MatchUpdateResultCopyWith(
          _MatchUpdateResult value, $Res Function(_MatchUpdateResult) _then) =
      __$MatchUpdateResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<GameMatch> updatedMatches,
      String sourceName,
      bool requiresStateUpdate});
}

/// @nodoc
class __$MatchUpdateResultCopyWithImpl<$Res>
    implements _$MatchUpdateResultCopyWith<$Res> {
  __$MatchUpdateResultCopyWithImpl(this._self, this._then);

  final _MatchUpdateResult _self;
  final $Res Function(_MatchUpdateResult) _then;

  /// Create a copy of MatchUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? updatedMatches = null,
    Object? sourceName = null,
    Object? requiresStateUpdate = null,
  }) {
    return _then(_MatchUpdateResult(
      updatedMatches: null == updatedMatches
          ? _self._updatedMatches
          : updatedMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      requiresStateUpdate: null == requiresStateUpdate
          ? _self.requiresStateUpdate
          : requiresStateUpdate // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$SubscriptionResult {
  bool get subscribed;
  String? get message;

  /// Create a copy of SubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SubscriptionResultCopyWith<SubscriptionResult> get copyWith =>
      _$SubscriptionResultCopyWithImpl<SubscriptionResult>(
          this as SubscriptionResult, _$identity);

  /// Serializes this SubscriptionResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SubscriptionResult &&
            (identical(other.subscribed, subscribed) ||
                other.subscribed == subscribed) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, subscribed, message);

  @override
  String toString() {
    return 'SubscriptionResult(subscribed: $subscribed, message: $message)';
  }
}

/// @nodoc
abstract mixin class $SubscriptionResultCopyWith<$Res> {
  factory $SubscriptionResultCopyWith(
          SubscriptionResult value, $Res Function(SubscriptionResult) _then) =
      _$SubscriptionResultCopyWithImpl;
  @useResult
  $Res call({bool subscribed, String? message});
}

/// @nodoc
class _$SubscriptionResultCopyWithImpl<$Res>
    implements $SubscriptionResultCopyWith<$Res> {
  _$SubscriptionResultCopyWithImpl(this._self, this._then);

  final SubscriptionResult _self;
  final $Res Function(SubscriptionResult) _then;

  /// Create a copy of SubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subscribed = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      subscribed: null == subscribed
          ? _self.subscribed
          : subscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SubscriptionResult implements SubscriptionResult {
  const _SubscriptionResult({this.subscribed = false, this.message});
  factory _SubscriptionResult.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionResultFromJson(json);

  @override
  @JsonKey()
  final bool subscribed;
  @override
  final String? message;

  /// Create a copy of SubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SubscriptionResultCopyWith<_SubscriptionResult> get copyWith =>
      __$SubscriptionResultCopyWithImpl<_SubscriptionResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SubscriptionResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SubscriptionResult &&
            (identical(other.subscribed, subscribed) ||
                other.subscribed == subscribed) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, subscribed, message);

  @override
  String toString() {
    return 'SubscriptionResult(subscribed: $subscribed, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$SubscriptionResultCopyWith<$Res>
    implements $SubscriptionResultCopyWith<$Res> {
  factory _$SubscriptionResultCopyWith(
          _SubscriptionResult value, $Res Function(_SubscriptionResult) _then) =
      __$SubscriptionResultCopyWithImpl;
  @override
  @useResult
  $Res call({bool subscribed, String? message});
}

/// @nodoc
class __$SubscriptionResultCopyWithImpl<$Res>
    implements _$SubscriptionResultCopyWith<$Res> {
  __$SubscriptionResultCopyWithImpl(this._self, this._then);

  final _SubscriptionResult _self;
  final $Res Function(_SubscriptionResult) _then;

  /// Create a copy of SubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? subscribed = null,
    Object? message = freezed,
  }) {
    return _then(_SubscriptionResult(
      subscribed: null == subscribed
          ? _self.subscribed
          : subscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$UnsubscriptionResult {
  bool get unsubscribed;
  String? get message;

  /// Create a copy of UnsubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnsubscriptionResultCopyWith<UnsubscriptionResult> get copyWith =>
      _$UnsubscriptionResultCopyWithImpl<UnsubscriptionResult>(
          this as UnsubscriptionResult, _$identity);

  /// Serializes this UnsubscriptionResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnsubscriptionResult &&
            (identical(other.unsubscribed, unsubscribed) ||
                other.unsubscribed == unsubscribed) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, unsubscribed, message);

  @override
  String toString() {
    return 'UnsubscriptionResult(unsubscribed: $unsubscribed, message: $message)';
  }
}

/// @nodoc
abstract mixin class $UnsubscriptionResultCopyWith<$Res> {
  factory $UnsubscriptionResultCopyWith(UnsubscriptionResult value,
          $Res Function(UnsubscriptionResult) _then) =
      _$UnsubscriptionResultCopyWithImpl;
  @useResult
  $Res call({bool unsubscribed, String? message});
}

/// @nodoc
class _$UnsubscriptionResultCopyWithImpl<$Res>
    implements $UnsubscriptionResultCopyWith<$Res> {
  _$UnsubscriptionResultCopyWithImpl(this._self, this._then);

  final UnsubscriptionResult _self;
  final $Res Function(UnsubscriptionResult) _then;

  /// Create a copy of UnsubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unsubscribed = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      unsubscribed: null == unsubscribed
          ? _self.unsubscribed
          : unsubscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _UnsubscriptionResult implements UnsubscriptionResult {
  const _UnsubscriptionResult({this.unsubscribed = false, this.message});
  factory _UnsubscriptionResult.fromJson(Map<String, dynamic> json) =>
      _$UnsubscriptionResultFromJson(json);

  @override
  @JsonKey()
  final bool unsubscribed;
  @override
  final String? message;

  /// Create a copy of UnsubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UnsubscriptionResultCopyWith<_UnsubscriptionResult> get copyWith =>
      __$UnsubscriptionResultCopyWithImpl<_UnsubscriptionResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UnsubscriptionResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UnsubscriptionResult &&
            (identical(other.unsubscribed, unsubscribed) ||
                other.unsubscribed == unsubscribed) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, unsubscribed, message);

  @override
  String toString() {
    return 'UnsubscriptionResult(unsubscribed: $unsubscribed, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$UnsubscriptionResultCopyWith<$Res>
    implements $UnsubscriptionResultCopyWith<$Res> {
  factory _$UnsubscriptionResultCopyWith(_UnsubscriptionResult value,
          $Res Function(_UnsubscriptionResult) _then) =
      __$UnsubscriptionResultCopyWithImpl;
  @override
  @useResult
  $Res call({bool unsubscribed, String? message});
}

/// @nodoc
class __$UnsubscriptionResultCopyWithImpl<$Res>
    implements _$UnsubscriptionResultCopyWith<$Res> {
  __$UnsubscriptionResultCopyWithImpl(this._self, this._then);

  final _UnsubscriptionResult _self;
  final $Res Function(_UnsubscriptionResult) _then;

  /// Create a copy of UnsubscriptionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? unsubscribed = null,
    Object? message = freezed,
  }) {
    return _then(_UnsubscriptionResult(
      unsubscribed: null == unsubscribed
          ? _self.unsubscribed
          : unsubscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$SourceRemovalResult {
  bool get sourceRemoved;
  String? get removedSourceName;
  String? get message;

  /// Create a copy of SourceRemovalResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SourceRemovalResultCopyWith<SourceRemovalResult> get copyWith =>
      _$SourceRemovalResultCopyWithImpl<SourceRemovalResult>(
          this as SourceRemovalResult, _$identity);

  /// Serializes this SourceRemovalResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SourceRemovalResult &&
            (identical(other.sourceRemoved, sourceRemoved) ||
                other.sourceRemoved == sourceRemoved) &&
            (identical(other.removedSourceName, removedSourceName) ||
                other.removedSourceName == removedSourceName) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, sourceRemoved, removedSourceName, message);

  @override
  String toString() {
    return 'SourceRemovalResult(sourceRemoved: $sourceRemoved, removedSourceName: $removedSourceName, message: $message)';
  }
}

/// @nodoc
abstract mixin class $SourceRemovalResultCopyWith<$Res> {
  factory $SourceRemovalResultCopyWith(
          SourceRemovalResult value, $Res Function(SourceRemovalResult) _then) =
      _$SourceRemovalResultCopyWithImpl;
  @useResult
  $Res call({bool sourceRemoved, String? removedSourceName, String? message});
}

/// @nodoc
class _$SourceRemovalResultCopyWithImpl<$Res>
    implements $SourceRemovalResultCopyWith<$Res> {
  _$SourceRemovalResultCopyWithImpl(this._self, this._then);

  final SourceRemovalResult _self;
  final $Res Function(SourceRemovalResult) _then;

  /// Create a copy of SourceRemovalResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceRemoved = null,
    Object? removedSourceName = freezed,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      sourceRemoved: null == sourceRemoved
          ? _self.sourceRemoved
          : sourceRemoved // ignore: cast_nullable_to_non_nullable
              as bool,
      removedSourceName: freezed == removedSourceName
          ? _self.removedSourceName
          : removedSourceName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SourceRemovalResult implements SourceRemovalResult {
  const _SourceRemovalResult(
      {this.sourceRemoved = false, this.removedSourceName, this.message});
  factory _SourceRemovalResult.fromJson(Map<String, dynamic> json) =>
      _$SourceRemovalResultFromJson(json);

  @override
  @JsonKey()
  final bool sourceRemoved;
  @override
  final String? removedSourceName;
  @override
  final String? message;

  /// Create a copy of SourceRemovalResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SourceRemovalResultCopyWith<_SourceRemovalResult> get copyWith =>
      __$SourceRemovalResultCopyWithImpl<_SourceRemovalResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SourceRemovalResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SourceRemovalResult &&
            (identical(other.sourceRemoved, sourceRemoved) ||
                other.sourceRemoved == sourceRemoved) &&
            (identical(other.removedSourceName, removedSourceName) ||
                other.removedSourceName == removedSourceName) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, sourceRemoved, removedSourceName, message);

  @override
  String toString() {
    return 'SourceRemovalResult(sourceRemoved: $sourceRemoved, removedSourceName: $removedSourceName, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$SourceRemovalResultCopyWith<$Res>
    implements $SourceRemovalResultCopyWith<$Res> {
  factory _$SourceRemovalResultCopyWith(_SourceRemovalResult value,
          $Res Function(_SourceRemovalResult) _then) =
      __$SourceRemovalResultCopyWithImpl;
  @override
  @useResult
  $Res call({bool sourceRemoved, String? removedSourceName, String? message});
}

/// @nodoc
class __$SourceRemovalResultCopyWithImpl<$Res>
    implements _$SourceRemovalResultCopyWith<$Res> {
  __$SourceRemovalResultCopyWithImpl(this._self, this._then);

  final _SourceRemovalResult _self;
  final $Res Function(_SourceRemovalResult) _then;

  /// Create a copy of SourceRemovalResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceRemoved = null,
    Object? removedSourceName = freezed,
    Object? message = freezed,
  }) {
    return _then(_SourceRemovalResult(
      sourceRemoved: null == sourceRemoved
          ? _self.sourceRemoved
          : sourceRemoved // ignore: cast_nullable_to_non_nullable
              as bool,
      removedSourceName: freezed == removedSourceName
          ? _self.removedSourceName
          : removedSourceName // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
