import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_management_requests.freezed.dart';
part 'match_management_requests.g.dart';

/// Request model for creating a new match
@freezed
abstract class CreateMatchRequest with _$CreateMatchRequest {
  const factory CreateMatchRequest({
    required GameConfig gameConfig,
    required List<PlayerSlot> playerSlots,
    required String gameName,
    @Default(true) bool openForJoining,
  }) = _CreateMatchRequest;

  factory CreateMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchRequestFromJson(json);
}

/// Request model for updating a player type in a slot
@freezed
abstract class UpdatePlayerTypeRequest with _$UpdatePlayerTypeRequest {
  const factory UpdatePlayerTypeRequest({
    required int slotIndex,
    required PlayerType newType,
    required List<PlayerSlot> currentSlots,
    required GameConfig gameConfig,
    String? matchId,
    @Default(false) bool isSelectedMatch,
  }) = _UpdatePlayerTypeRequest;

  factory UpdatePlayerTypeRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePlayerTypeRequestFromJson(json);
}

/// Request model for joining a player slot
@freezed
abstract class JoinSlotRequest with _$JoinSlotRequest {
  const factory JoinSlotRequest({
    required int slotIndex,
    required List<PlayerSlot> currentSlots,
    String? playerId,
    String? matchId,
  }) = _JoinSlotRequest;

  factory JoinSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinSlotRequestFromJson(json);
}

/// Request model for loading match data
@freezed
abstract class LoadMatchDataRequest with _$LoadMatchDataRequest {
  const factory LoadMatchDataRequest({
    String? gameName,
    @Default(false) bool forceRefresh,
    Set<String>? specificSources,
  }) = _LoadMatchDataRequest;

  factory LoadMatchDataRequest.fromJson(Map<String, dynamic> json) =>
      _$LoadMatchDataRequestFromJson(json);
}

/// Request model for refreshing matches from a specific source
@freezed
abstract class RefreshMatchesRequest with _$RefreshMatchesRequest {
  const factory RefreshMatchesRequest({
    required String sourceName,
    String? gameName,
  }) = _RefreshMatchesRequest;

  factory RefreshMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshMatchesRequestFromJson(json);
}

/// Request model for joining a match
@freezed
abstract class JoinMatchRequest with _$JoinMatchRequest {
  const factory JoinMatchRequest({
    required String matchId,
    String? playerId,
  }) = _JoinMatchRequest;

  factory JoinMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchRequestFromJson(json);
}

/// Request model for leaving a match
@freezed
abstract class LeaveMatchRequest with _$LeaveMatchRequest {
  const factory LeaveMatchRequest({
    required String matchId,
    String? playerId,
  }) = _LeaveMatchRequest;

  factory LeaveMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$LeaveMatchRequestFromJson(json);
}

/// Request model for deleting a match
@freezed
abstract class DeleteMatchRequest with _$DeleteMatchRequest {
  const factory DeleteMatchRequest({
    required String matchId,
  }) = _DeleteMatchRequest;

  factory DeleteMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMatchRequestFromJson(json);
}

/// Request model for subscribing to real-time updates
@freezed
abstract class SubscribeToRealTimeUpdatesRequest with _$SubscribeToRealTimeUpdatesRequest {
  const factory SubscribeToRealTimeUpdatesRequest({
    String? gameName,
  }) = _SubscribeToRealTimeUpdatesRequest;

  factory SubscribeToRealTimeUpdatesRequest.fromJson(Map<String, dynamic> json) =>
      _$SubscribeToRealTimeUpdatesRequestFromJson(json);
}

/// Request model for unsubscribing from real-time updates
@freezed
abstract class UnsubscribeFromRealTimeUpdatesRequest with _$UnsubscribeFromRealTimeUpdatesRequest {
  const factory UnsubscribeFromRealTimeUpdatesRequest() = _UnsubscribeFromRealTimeUpdatesRequest;

  factory UnsubscribeFromRealTimeUpdatesRequest.fromJson(Map<String, dynamic> json) =>
      _$UnsubscribeFromRealTimeUpdatesRequestFromJson(json);
}

/// Request model for removing a match source
@freezed
abstract class RemoveMatchSourceRequest with _$RemoveMatchSourceRequest {
  const factory RemoveMatchSourceRequest({
    required String sourceName,
  }) = _RemoveMatchSourceRequest;

  factory RemoveMatchSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveMatchSourceRequestFromJson(json);
}
