// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_management_requests.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CreateMatchRequest _$CreateMatchRequestFromJson(Map<String, dynamic> json) =>
    _CreateMatchRequest(
      gameConfig:
          GameConfig.fromJson(json['gameConfig'] as Map<String, dynamic>),
      playerSlots: (json['playerSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      gameName: json['gameName'] as String,
      openForJoining: json['openForJoining'] as bool? ?? true,
    );

Map<String, dynamic> _$CreateMatchRequestToJson(_CreateMatchRequest instance) =>
    <String, dynamic>{
      'gameConfig': instance.gameConfig,
      'playerSlots': instance.playerSlots,
      'gameName': instance.gameName,
      'openForJoining': instance.openForJoining,
    };

_UpdatePlayerTypeRequest _$UpdatePlayerTypeRequestFromJson(
        Map<String, dynamic> json) =>
    _UpdatePlayerTypeRequest(
      slotIndex: (json['slotIndex'] as num).toInt(),
      newType: $enumDecode(_$PlayerTypeEnumMap, json['newType']),
      currentSlots: (json['currentSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      gameConfig:
          GameConfig.fromJson(json['gameConfig'] as Map<String, dynamic>),
      matchId: json['matchId'] as String?,
      isSelectedMatch: json['isSelectedMatch'] as bool? ?? false,
    );

Map<String, dynamic> _$UpdatePlayerTypeRequestToJson(
        _UpdatePlayerTypeRequest instance) =>
    <String, dynamic>{
      'slotIndex': instance.slotIndex,
      'newType': _$PlayerTypeEnumMap[instance.newType]!,
      'currentSlots': instance.currentSlots,
      'gameConfig': instance.gameConfig,
      'matchId': instance.matchId,
      'isSelectedMatch': instance.isSelectedMatch,
    };

const _$PlayerTypeEnumMap = {
  PlayerType.humanLocal: 'humanLocal',
  PlayerType.humanNetwork: 'humanNetwork',
  PlayerType.botLocal: 'botLocal',
  PlayerType.botNetwork: 'botNetwork',
};

_JoinSlotRequest _$JoinSlotRequestFromJson(Map<String, dynamic> json) =>
    _JoinSlotRequest(
      slotIndex: (json['slotIndex'] as num).toInt(),
      currentSlots: (json['currentSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      playerId: json['playerId'] as String?,
      matchId: json['matchId'] as String?,
    );

Map<String, dynamic> _$JoinSlotRequestToJson(_JoinSlotRequest instance) =>
    <String, dynamic>{
      'slotIndex': instance.slotIndex,
      'currentSlots': instance.currentSlots,
      'playerId': instance.playerId,
      'matchId': instance.matchId,
    };

_LoadMatchDataRequest _$LoadMatchDataRequestFromJson(
        Map<String, dynamic> json) =>
    _LoadMatchDataRequest(
      gameName: json['gameName'] as String?,
      forceRefresh: json['forceRefresh'] as bool? ?? false,
      specificSources: (json['specificSources'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toSet(),
    );

Map<String, dynamic> _$LoadMatchDataRequestToJson(
        _LoadMatchDataRequest instance) =>
    <String, dynamic>{
      'gameName': instance.gameName,
      'forceRefresh': instance.forceRefresh,
      'specificSources': instance.specificSources?.toList(),
    };

_RefreshMatchesRequest _$RefreshMatchesRequestFromJson(
        Map<String, dynamic> json) =>
    _RefreshMatchesRequest(
      sourceName: json['sourceName'] as String,
      gameName: json['gameName'] as String?,
    );

Map<String, dynamic> _$RefreshMatchesRequestToJson(
        _RefreshMatchesRequest instance) =>
    <String, dynamic>{
      'sourceName': instance.sourceName,
      'gameName': instance.gameName,
    };

_JoinMatchRequest _$JoinMatchRequestFromJson(Map<String, dynamic> json) =>
    _JoinMatchRequest(
      matchId: json['matchId'] as String,
      playerId: json['playerId'] as String?,
    );

Map<String, dynamic> _$JoinMatchRequestToJson(_JoinMatchRequest instance) =>
    <String, dynamic>{
      'matchId': instance.matchId,
      'playerId': instance.playerId,
    };

_LeaveMatchRequest _$LeaveMatchRequestFromJson(Map<String, dynamic> json) =>
    _LeaveMatchRequest(
      matchId: json['matchId'] as String,
      playerId: json['playerId'] as String?,
    );

Map<String, dynamic> _$LeaveMatchRequestToJson(_LeaveMatchRequest instance) =>
    <String, dynamic>{
      'matchId': instance.matchId,
      'playerId': instance.playerId,
    };

_DeleteMatchRequest _$DeleteMatchRequestFromJson(Map<String, dynamic> json) =>
    _DeleteMatchRequest(
      matchId: json['matchId'] as String,
    );

Map<String, dynamic> _$DeleteMatchRequestToJson(_DeleteMatchRequest instance) =>
    <String, dynamic>{
      'matchId': instance.matchId,
    };

_SubscribeToRealTimeUpdatesRequest _$SubscribeToRealTimeUpdatesRequestFromJson(
        Map<String, dynamic> json) =>
    _SubscribeToRealTimeUpdatesRequest(
      gameName: json['gameName'] as String?,
    );

Map<String, dynamic> _$SubscribeToRealTimeUpdatesRequestToJson(
        _SubscribeToRealTimeUpdatesRequest instance) =>
    <String, dynamic>{
      'gameName': instance.gameName,
    };

_UnsubscribeFromRealTimeUpdatesRequest
    _$UnsubscribeFromRealTimeUpdatesRequestFromJson(
            Map<String, dynamic> json) =>
        _UnsubscribeFromRealTimeUpdatesRequest();

Map<String, dynamic> _$UnsubscribeFromRealTimeUpdatesRequestToJson(
        _UnsubscribeFromRealTimeUpdatesRequest instance) =>
    <String, dynamic>{};

_RemoveMatchSourceRequest _$RemoveMatchSourceRequestFromJson(
        Map<String, dynamic> json) =>
    _RemoveMatchSourceRequest(
      sourceName: json['sourceName'] as String,
    );

Map<String, dynamic> _$RemoveMatchSourceRequestToJson(
        _RemoveMatchSourceRequest instance) =>
    <String, dynamic>{
      'sourceName': instance.sourceName,
    };
