// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_management_requests.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateMatchRequest {
  GameConfig get gameConfig;
  List<PlayerSlot> get playerSlots;
  String get gameName;
  bool get openForJoining;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMatchRequestCopyWith<CreateMatchRequest> get copyWith =>
      _$CreateMatchRequestCopyWithImpl<CreateMatchRequest>(
          this as CreateMatchRequest, _$identity);

  /// Serializes this CreateMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMatchRequest &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.openForJoining, openForJoining) ||
                other.openForJoining == openForJoining));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameConfig,
      const DeepCollectionEquality().hash(playerSlots),
      gameName,
      openForJoining);

  @override
  String toString() {
    return 'CreateMatchRequest(gameConfig: $gameConfig, playerSlots: $playerSlots, gameName: $gameName, openForJoining: $openForJoining)';
  }
}

/// @nodoc
abstract mixin class $CreateMatchRequestCopyWith<$Res> {
  factory $CreateMatchRequestCopyWith(
          CreateMatchRequest value, $Res Function(CreateMatchRequest) _then) =
      _$CreateMatchRequestCopyWithImpl;
  @useResult
  $Res call(
      {GameConfig gameConfig,
      List<PlayerSlot> playerSlots,
      String gameName,
      bool openForJoining});

  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class _$CreateMatchRequestCopyWithImpl<$Res>
    implements $CreateMatchRequestCopyWith<$Res> {
  _$CreateMatchRequestCopyWithImpl(this._self, this._then);

  final CreateMatchRequest _self;
  final $Res Function(CreateMatchRequest) _then;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameConfig = null,
    Object? playerSlots = null,
    Object? gameName = null,
    Object? openForJoining = null,
  }) {
    return _then(_self.copyWith(
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameName: null == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String,
      openForJoining: null == openForJoining
          ? _self.openForJoining
          : openForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMatchRequest implements CreateMatchRequest {
  const _CreateMatchRequest(
      {required this.gameConfig,
      required final List<PlayerSlot> playerSlots,
      required this.gameName,
      this.openForJoining = true})
      : _playerSlots = playerSlots;
  factory _CreateMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchRequestFromJson(json);

  @override
  final GameConfig gameConfig;
  final List<PlayerSlot> _playerSlots;
  @override
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

  @override
  final String gameName;
  @override
  @JsonKey()
  final bool openForJoining;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMatchRequestCopyWith<_CreateMatchRequest> get copyWith =>
      __$CreateMatchRequestCopyWithImpl<_CreateMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMatchRequest &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.openForJoining, openForJoining) ||
                other.openForJoining == openForJoining));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameConfig,
      const DeepCollectionEquality().hash(_playerSlots),
      gameName,
      openForJoining);

  @override
  String toString() {
    return 'CreateMatchRequest(gameConfig: $gameConfig, playerSlots: $playerSlots, gameName: $gameName, openForJoining: $openForJoining)';
  }
}

/// @nodoc
abstract mixin class _$CreateMatchRequestCopyWith<$Res>
    implements $CreateMatchRequestCopyWith<$Res> {
  factory _$CreateMatchRequestCopyWith(
          _CreateMatchRequest value, $Res Function(_CreateMatchRequest) _then) =
      __$CreateMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameConfig gameConfig,
      List<PlayerSlot> playerSlots,
      String gameName,
      bool openForJoining});

  @override
  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class __$CreateMatchRequestCopyWithImpl<$Res>
    implements _$CreateMatchRequestCopyWith<$Res> {
  __$CreateMatchRequestCopyWithImpl(this._self, this._then);

  final _CreateMatchRequest _self;
  final $Res Function(_CreateMatchRequest) _then;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameConfig = null,
    Object? playerSlots = null,
    Object? gameName = null,
    Object? openForJoining = null,
  }) {
    return _then(_CreateMatchRequest(
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameName: null == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String,
      openForJoining: null == openForJoining
          ? _self.openForJoining
          : openForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
mixin _$UpdatePlayerTypeRequest {
  int get slotIndex;
  PlayerType get newType;
  List<PlayerSlot> get currentSlots;
  GameConfig get gameConfig;
  String? get matchId;
  bool get isSelectedMatch;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdatePlayerTypeRequestCopyWith<UpdatePlayerTypeRequest> get copyWith =>
      _$UpdatePlayerTypeRequestCopyWithImpl<UpdatePlayerTypeRequest>(
          this as UpdatePlayerTypeRequest, _$identity);

  /// Serializes this UpdatePlayerTypeRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdatePlayerTypeRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.newType, newType) || other.newType == newType) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots) &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.isSelectedMatch, isSelectedMatch) ||
                other.isSelectedMatch == isSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      slotIndex,
      newType,
      const DeepCollectionEquality().hash(currentSlots),
      gameConfig,
      matchId,
      isSelectedMatch);

  @override
  String toString() {
    return 'UpdatePlayerTypeRequest(slotIndex: $slotIndex, newType: $newType, currentSlots: $currentSlots, gameConfig: $gameConfig, matchId: $matchId, isSelectedMatch: $isSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class $UpdatePlayerTypeRequestCopyWith<$Res> {
  factory $UpdatePlayerTypeRequestCopyWith(UpdatePlayerTypeRequest value,
          $Res Function(UpdatePlayerTypeRequest) _then) =
      _$UpdatePlayerTypeRequestCopyWithImpl;
  @useResult
  $Res call(
      {int slotIndex,
      PlayerType newType,
      List<PlayerSlot> currentSlots,
      GameConfig gameConfig,
      String? matchId,
      bool isSelectedMatch});

  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class _$UpdatePlayerTypeRequestCopyWithImpl<$Res>
    implements $UpdatePlayerTypeRequestCopyWith<$Res> {
  _$UpdatePlayerTypeRequestCopyWithImpl(this._self, this._then);

  final UpdatePlayerTypeRequest _self;
  final $Res Function(UpdatePlayerTypeRequest) _then;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? slotIndex = null,
    Object? newType = null,
    Object? currentSlots = null,
    Object? gameConfig = null,
    Object? matchId = freezed,
    Object? isSelectedMatch = null,
  }) {
    return _then(_self.copyWith(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      newType: null == newType
          ? _self.newType
          : newType // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelectedMatch: null == isSelectedMatch
          ? _self.isSelectedMatch
          : isSelectedMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _UpdatePlayerTypeRequest implements UpdatePlayerTypeRequest {
  const _UpdatePlayerTypeRequest(
      {required this.slotIndex,
      required this.newType,
      required final List<PlayerSlot> currentSlots,
      required this.gameConfig,
      this.matchId,
      this.isSelectedMatch = false})
      : _currentSlots = currentSlots;
  factory _UpdatePlayerTypeRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePlayerTypeRequestFromJson(json);

  @override
  final int slotIndex;
  @override
  final PlayerType newType;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  @override
  final GameConfig gameConfig;
  @override
  final String? matchId;
  @override
  @JsonKey()
  final bool isSelectedMatch;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdatePlayerTypeRequestCopyWith<_UpdatePlayerTypeRequest> get copyWith =>
      __$UpdatePlayerTypeRequestCopyWithImpl<_UpdatePlayerTypeRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UpdatePlayerTypeRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatePlayerTypeRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.newType, newType) || other.newType == newType) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots) &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.isSelectedMatch, isSelectedMatch) ||
                other.isSelectedMatch == isSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      slotIndex,
      newType,
      const DeepCollectionEquality().hash(_currentSlots),
      gameConfig,
      matchId,
      isSelectedMatch);

  @override
  String toString() {
    return 'UpdatePlayerTypeRequest(slotIndex: $slotIndex, newType: $newType, currentSlots: $currentSlots, gameConfig: $gameConfig, matchId: $matchId, isSelectedMatch: $isSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class _$UpdatePlayerTypeRequestCopyWith<$Res>
    implements $UpdatePlayerTypeRequestCopyWith<$Res> {
  factory _$UpdatePlayerTypeRequestCopyWith(_UpdatePlayerTypeRequest value,
          $Res Function(_UpdatePlayerTypeRequest) _then) =
      __$UpdatePlayerTypeRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int slotIndex,
      PlayerType newType,
      List<PlayerSlot> currentSlots,
      GameConfig gameConfig,
      String? matchId,
      bool isSelectedMatch});

  @override
  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class __$UpdatePlayerTypeRequestCopyWithImpl<$Res>
    implements _$UpdatePlayerTypeRequestCopyWith<$Res> {
  __$UpdatePlayerTypeRequestCopyWithImpl(this._self, this._then);

  final _UpdatePlayerTypeRequest _self;
  final $Res Function(_UpdatePlayerTypeRequest) _then;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? slotIndex = null,
    Object? newType = null,
    Object? currentSlots = null,
    Object? gameConfig = null,
    Object? matchId = freezed,
    Object? isSelectedMatch = null,
  }) {
    return _then(_UpdatePlayerTypeRequest(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      newType: null == newType
          ? _self.newType
          : newType // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelectedMatch: null == isSelectedMatch
          ? _self.isSelectedMatch
          : isSelectedMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
mixin _$JoinSlotRequest {
  int get slotIndex;
  List<PlayerSlot> get currentSlots;
  String? get playerId;
  String? get matchId;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinSlotRequestCopyWith<JoinSlotRequest> get copyWith =>
      _$JoinSlotRequestCopyWithImpl<JoinSlotRequest>(
          this as JoinSlotRequest, _$identity);

  /// Serializes this JoinSlotRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinSlotRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, slotIndex,
      const DeepCollectionEquality().hash(currentSlots), playerId, matchId);

  @override
  String toString() {
    return 'JoinSlotRequest(slotIndex: $slotIndex, currentSlots: $currentSlots, playerId: $playerId, matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class $JoinSlotRequestCopyWith<$Res> {
  factory $JoinSlotRequestCopyWith(
          JoinSlotRequest value, $Res Function(JoinSlotRequest) _then) =
      _$JoinSlotRequestCopyWithImpl;
  @useResult
  $Res call(
      {int slotIndex,
      List<PlayerSlot> currentSlots,
      String? playerId,
      String? matchId});
}

/// @nodoc
class _$JoinSlotRequestCopyWithImpl<$Res>
    implements $JoinSlotRequestCopyWith<$Res> {
  _$JoinSlotRequestCopyWithImpl(this._self, this._then);

  final JoinSlotRequest _self;
  final $Res Function(JoinSlotRequest) _then;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? slotIndex = null,
    Object? currentSlots = null,
    Object? playerId = freezed,
    Object? matchId = freezed,
  }) {
    return _then(_self.copyWith(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinSlotRequest implements JoinSlotRequest {
  const _JoinSlotRequest(
      {required this.slotIndex,
      required final List<PlayerSlot> currentSlots,
      this.playerId,
      this.matchId})
      : _currentSlots = currentSlots;
  factory _JoinSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinSlotRequestFromJson(json);

  @override
  final int slotIndex;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  @override
  final String? playerId;
  @override
  final String? matchId;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinSlotRequestCopyWith<_JoinSlotRequest> get copyWith =>
      __$JoinSlotRequestCopyWithImpl<_JoinSlotRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinSlotRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinSlotRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, slotIndex,
      const DeepCollectionEquality().hash(_currentSlots), playerId, matchId);

  @override
  String toString() {
    return 'JoinSlotRequest(slotIndex: $slotIndex, currentSlots: $currentSlots, playerId: $playerId, matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class _$JoinSlotRequestCopyWith<$Res>
    implements $JoinSlotRequestCopyWith<$Res> {
  factory _$JoinSlotRequestCopyWith(
          _JoinSlotRequest value, $Res Function(_JoinSlotRequest) _then) =
      __$JoinSlotRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int slotIndex,
      List<PlayerSlot> currentSlots,
      String? playerId,
      String? matchId});
}

/// @nodoc
class __$JoinSlotRequestCopyWithImpl<$Res>
    implements _$JoinSlotRequestCopyWith<$Res> {
  __$JoinSlotRequestCopyWithImpl(this._self, this._then);

  final _JoinSlotRequest _self;
  final $Res Function(_JoinSlotRequest) _then;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? slotIndex = null,
    Object? currentSlots = null,
    Object? playerId = freezed,
    Object? matchId = freezed,
  }) {
    return _then(_JoinSlotRequest(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$LoadMatchDataRequest {
  String? get gameName;
  bool get forceRefresh;
  Set<String>? get specificSources;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadMatchDataRequestCopyWith<LoadMatchDataRequest> get copyWith =>
      _$LoadMatchDataRequestCopyWithImpl<LoadMatchDataRequest>(
          this as LoadMatchDataRequest, _$identity);

  /// Serializes this LoadMatchDataRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadMatchDataRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.forceRefresh, forceRefresh) ||
                other.forceRefresh == forceRefresh) &&
            const DeepCollectionEquality()
                .equals(other.specificSources, specificSources));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName, forceRefresh,
      const DeepCollectionEquality().hash(specificSources));

  @override
  String toString() {
    return 'LoadMatchDataRequest(gameName: $gameName, forceRefresh: $forceRefresh, specificSources: $specificSources)';
  }
}

/// @nodoc
abstract mixin class $LoadMatchDataRequestCopyWith<$Res> {
  factory $LoadMatchDataRequestCopyWith(LoadMatchDataRequest value,
          $Res Function(LoadMatchDataRequest) _then) =
      _$LoadMatchDataRequestCopyWithImpl;
  @useResult
  $Res call(
      {String? gameName, bool forceRefresh, Set<String>? specificSources});
}

/// @nodoc
class _$LoadMatchDataRequestCopyWithImpl<$Res>
    implements $LoadMatchDataRequestCopyWith<$Res> {
  _$LoadMatchDataRequestCopyWithImpl(this._self, this._then);

  final LoadMatchDataRequest _self;
  final $Res Function(LoadMatchDataRequest) _then;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameName = freezed,
    Object? forceRefresh = null,
    Object? specificSources = freezed,
  }) {
    return _then(_self.copyWith(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      forceRefresh: null == forceRefresh
          ? _self.forceRefresh
          : forceRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      specificSources: freezed == specificSources
          ? _self.specificSources
          : specificSources // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LoadMatchDataRequest implements LoadMatchDataRequest {
  const _LoadMatchDataRequest(
      {this.gameName,
      this.forceRefresh = false,
      final Set<String>? specificSources})
      : _specificSources = specificSources;
  factory _LoadMatchDataRequest.fromJson(Map<String, dynamic> json) =>
      _$LoadMatchDataRequestFromJson(json);

  @override
  final String? gameName;
  @override
  @JsonKey()
  final bool forceRefresh;
  final Set<String>? _specificSources;
  @override
  Set<String>? get specificSources {
    final value = _specificSources;
    if (value == null) return null;
    if (_specificSources is EqualUnmodifiableSetView) return _specificSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(value);
  }

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadMatchDataRequestCopyWith<_LoadMatchDataRequest> get copyWith =>
      __$LoadMatchDataRequestCopyWithImpl<_LoadMatchDataRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LoadMatchDataRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadMatchDataRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.forceRefresh, forceRefresh) ||
                other.forceRefresh == forceRefresh) &&
            const DeepCollectionEquality()
                .equals(other._specificSources, _specificSources));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName, forceRefresh,
      const DeepCollectionEquality().hash(_specificSources));

  @override
  String toString() {
    return 'LoadMatchDataRequest(gameName: $gameName, forceRefresh: $forceRefresh, specificSources: $specificSources)';
  }
}

/// @nodoc
abstract mixin class _$LoadMatchDataRequestCopyWith<$Res>
    implements $LoadMatchDataRequestCopyWith<$Res> {
  factory _$LoadMatchDataRequestCopyWith(_LoadMatchDataRequest value,
          $Res Function(_LoadMatchDataRequest) _then) =
      __$LoadMatchDataRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? gameName, bool forceRefresh, Set<String>? specificSources});
}

/// @nodoc
class __$LoadMatchDataRequestCopyWithImpl<$Res>
    implements _$LoadMatchDataRequestCopyWith<$Res> {
  __$LoadMatchDataRequestCopyWithImpl(this._self, this._then);

  final _LoadMatchDataRequest _self;
  final $Res Function(_LoadMatchDataRequest) _then;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameName = freezed,
    Object? forceRefresh = null,
    Object? specificSources = freezed,
  }) {
    return _then(_LoadMatchDataRequest(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      forceRefresh: null == forceRefresh
          ? _self.forceRefresh
          : forceRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      specificSources: freezed == specificSources
          ? _self._specificSources
          : specificSources // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
    ));
  }
}

/// @nodoc
mixin _$RefreshMatchesRequest {
  String get sourceName;
  String? get gameName;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshMatchesRequestCopyWith<RefreshMatchesRequest> get copyWith =>
      _$RefreshMatchesRequestCopyWithImpl<RefreshMatchesRequest>(
          this as RefreshMatchesRequest, _$identity);

  /// Serializes this RefreshMatchesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshMatchesRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName, gameName);

  @override
  String toString() {
    return 'RefreshMatchesRequest(sourceName: $sourceName, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class $RefreshMatchesRequestCopyWith<$Res> {
  factory $RefreshMatchesRequestCopyWith(RefreshMatchesRequest value,
          $Res Function(RefreshMatchesRequest) _then) =
      _$RefreshMatchesRequestCopyWithImpl;
  @useResult
  $Res call({String sourceName, String? gameName});
}

/// @nodoc
class _$RefreshMatchesRequestCopyWithImpl<$Res>
    implements $RefreshMatchesRequestCopyWith<$Res> {
  _$RefreshMatchesRequestCopyWithImpl(this._self, this._then);

  final RefreshMatchesRequest _self;
  final $Res Function(RefreshMatchesRequest) _then;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
    Object? gameName = freezed,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshMatchesRequest implements RefreshMatchesRequest {
  const _RefreshMatchesRequest({required this.sourceName, this.gameName});
  factory _RefreshMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshMatchesRequestFromJson(json);

  @override
  final String sourceName;
  @override
  final String? gameName;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshMatchesRequestCopyWith<_RefreshMatchesRequest> get copyWith =>
      __$RefreshMatchesRequestCopyWithImpl<_RefreshMatchesRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshMatchesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshMatchesRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName, gameName);

  @override
  String toString() {
    return 'RefreshMatchesRequest(sourceName: $sourceName, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class _$RefreshMatchesRequestCopyWith<$Res>
    implements $RefreshMatchesRequestCopyWith<$Res> {
  factory _$RefreshMatchesRequestCopyWith(_RefreshMatchesRequest value,
          $Res Function(_RefreshMatchesRequest) _then) =
      __$RefreshMatchesRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String sourceName, String? gameName});
}

/// @nodoc
class __$RefreshMatchesRequestCopyWithImpl<$Res>
    implements _$RefreshMatchesRequestCopyWith<$Res> {
  __$RefreshMatchesRequestCopyWithImpl(this._self, this._then);

  final _RefreshMatchesRequest _self;
  final $Res Function(_RefreshMatchesRequest) _then;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
    Object? gameName = freezed,
  }) {
    return _then(_RefreshMatchesRequest(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$JoinMatchRequest {
  String get matchId;
  String? get playerId;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinMatchRequestCopyWith<JoinMatchRequest> get copyWith =>
      _$JoinMatchRequestCopyWithImpl<JoinMatchRequest>(
          this as JoinMatchRequest, _$identity);

  /// Serializes this JoinMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'JoinMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class $JoinMatchRequestCopyWith<$Res> {
  factory $JoinMatchRequestCopyWith(
          JoinMatchRequest value, $Res Function(JoinMatchRequest) _then) =
      _$JoinMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class _$JoinMatchRequestCopyWithImpl<$Res>
    implements $JoinMatchRequestCopyWith<$Res> {
  _$JoinMatchRequestCopyWithImpl(this._self, this._then);

  final JoinMatchRequest _self;
  final $Res Function(JoinMatchRequest) _then;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinMatchRequest implements JoinMatchRequest {
  const _JoinMatchRequest({required this.matchId, this.playerId});
  factory _JoinMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchRequestFromJson(json);

  @override
  final String matchId;
  @override
  final String? playerId;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinMatchRequestCopyWith<_JoinMatchRequest> get copyWith =>
      __$JoinMatchRequestCopyWithImpl<_JoinMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'JoinMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class _$JoinMatchRequestCopyWith<$Res>
    implements $JoinMatchRequestCopyWith<$Res> {
  factory _$JoinMatchRequestCopyWith(
          _JoinMatchRequest value, $Res Function(_JoinMatchRequest) _then) =
      __$JoinMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class __$JoinMatchRequestCopyWithImpl<$Res>
    implements _$JoinMatchRequestCopyWith<$Res> {
  __$JoinMatchRequestCopyWithImpl(this._self, this._then);

  final _JoinMatchRequest _self;
  final $Res Function(_JoinMatchRequest) _then;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_JoinMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$LeaveMatchRequest {
  String get matchId;
  String? get playerId;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LeaveMatchRequestCopyWith<LeaveMatchRequest> get copyWith =>
      _$LeaveMatchRequestCopyWithImpl<LeaveMatchRequest>(
          this as LeaveMatchRequest, _$identity);

  /// Serializes this LeaveMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LeaveMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'LeaveMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class $LeaveMatchRequestCopyWith<$Res> {
  factory $LeaveMatchRequestCopyWith(
          LeaveMatchRequest value, $Res Function(LeaveMatchRequest) _then) =
      _$LeaveMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class _$LeaveMatchRequestCopyWithImpl<$Res>
    implements $LeaveMatchRequestCopyWith<$Res> {
  _$LeaveMatchRequestCopyWithImpl(this._self, this._then);

  final LeaveMatchRequest _self;
  final $Res Function(LeaveMatchRequest) _then;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LeaveMatchRequest implements LeaveMatchRequest {
  const _LeaveMatchRequest({required this.matchId, this.playerId});
  factory _LeaveMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$LeaveMatchRequestFromJson(json);

  @override
  final String matchId;
  @override
  final String? playerId;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LeaveMatchRequestCopyWith<_LeaveMatchRequest> get copyWith =>
      __$LeaveMatchRequestCopyWithImpl<_LeaveMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LeaveMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LeaveMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'LeaveMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class _$LeaveMatchRequestCopyWith<$Res>
    implements $LeaveMatchRequestCopyWith<$Res> {
  factory _$LeaveMatchRequestCopyWith(
          _LeaveMatchRequest value, $Res Function(_LeaveMatchRequest) _then) =
      __$LeaveMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class __$LeaveMatchRequestCopyWithImpl<$Res>
    implements _$LeaveMatchRequestCopyWith<$Res> {
  __$LeaveMatchRequestCopyWithImpl(this._self, this._then);

  final _LeaveMatchRequest _self;
  final $Res Function(_LeaveMatchRequest) _then;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_LeaveMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$DeleteMatchRequest {
  String get matchId;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeleteMatchRequestCopyWith<DeleteMatchRequest> get copyWith =>
      _$DeleteMatchRequestCopyWithImpl<DeleteMatchRequest>(
          this as DeleteMatchRequest, _$identity);

  /// Serializes this DeleteMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeleteMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId);

  @override
  String toString() {
    return 'DeleteMatchRequest(matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class $DeleteMatchRequestCopyWith<$Res> {
  factory $DeleteMatchRequestCopyWith(
          DeleteMatchRequest value, $Res Function(DeleteMatchRequest) _then) =
      _$DeleteMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId});
}

/// @nodoc
class _$DeleteMatchRequestCopyWithImpl<$Res>
    implements $DeleteMatchRequestCopyWith<$Res> {
  _$DeleteMatchRequestCopyWithImpl(this._self, this._then);

  final DeleteMatchRequest _self;
  final $Res Function(DeleteMatchRequest) _then;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _DeleteMatchRequest implements DeleteMatchRequest {
  const _DeleteMatchRequest({required this.matchId});
  factory _DeleteMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMatchRequestFromJson(json);

  @override
  final String matchId;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeleteMatchRequestCopyWith<_DeleteMatchRequest> get copyWith =>
      __$DeleteMatchRequestCopyWithImpl<_DeleteMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DeleteMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeleteMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId);

  @override
  String toString() {
    return 'DeleteMatchRequest(matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class _$DeleteMatchRequestCopyWith<$Res>
    implements $DeleteMatchRequestCopyWith<$Res> {
  factory _$DeleteMatchRequestCopyWith(
          _DeleteMatchRequest value, $Res Function(_DeleteMatchRequest) _then) =
      __$DeleteMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId});
}

/// @nodoc
class __$DeleteMatchRequestCopyWithImpl<$Res>
    implements _$DeleteMatchRequestCopyWith<$Res> {
  __$DeleteMatchRequestCopyWithImpl(this._self, this._then);

  final _DeleteMatchRequest _self;
  final $Res Function(_DeleteMatchRequest) _then;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
  }) {
    return _then(_DeleteMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$SubscribeToRealTimeUpdatesRequest {
  String? get gameName;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SubscribeToRealTimeUpdatesRequestCopyWith<SubscribeToRealTimeUpdatesRequest>
      get copyWith => _$SubscribeToRealTimeUpdatesRequestCopyWithImpl<
              SubscribeToRealTimeUpdatesRequest>(
          this as SubscribeToRealTimeUpdatesRequest, _$identity);

  /// Serializes this SubscribeToRealTimeUpdatesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SubscribeToRealTimeUpdatesRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName);

  @override
  String toString() {
    return 'SubscribeToRealTimeUpdatesRequest(gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  factory $SubscribeToRealTimeUpdatesRequestCopyWith(
          SubscribeToRealTimeUpdatesRequest value,
          $Res Function(SubscribeToRealTimeUpdatesRequest) _then) =
      _$SubscribeToRealTimeUpdatesRequestCopyWithImpl;
  @useResult
  $Res call({String? gameName});
}

/// @nodoc
class _$SubscribeToRealTimeUpdatesRequestCopyWithImpl<$Res>
    implements $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  _$SubscribeToRealTimeUpdatesRequestCopyWithImpl(this._self, this._then);

  final SubscribeToRealTimeUpdatesRequest _self;
  final $Res Function(SubscribeToRealTimeUpdatesRequest) _then;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameName = freezed,
  }) {
    return _then(_self.copyWith(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SubscribeToRealTimeUpdatesRequest
    implements SubscribeToRealTimeUpdatesRequest {
  const _SubscribeToRealTimeUpdatesRequest({this.gameName});
  factory _SubscribeToRealTimeUpdatesRequest.fromJson(
          Map<String, dynamic> json) =>
      _$SubscribeToRealTimeUpdatesRequestFromJson(json);

  @override
  final String? gameName;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SubscribeToRealTimeUpdatesRequestCopyWith<
          _SubscribeToRealTimeUpdatesRequest>
      get copyWith => __$SubscribeToRealTimeUpdatesRequestCopyWithImpl<
          _SubscribeToRealTimeUpdatesRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SubscribeToRealTimeUpdatesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SubscribeToRealTimeUpdatesRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName);

  @override
  String toString() {
    return 'SubscribeToRealTimeUpdatesRequest(gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class _$SubscribeToRealTimeUpdatesRequestCopyWith<$Res>
    implements $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  factory _$SubscribeToRealTimeUpdatesRequestCopyWith(
          _SubscribeToRealTimeUpdatesRequest value,
          $Res Function(_SubscribeToRealTimeUpdatesRequest) _then) =
      __$SubscribeToRealTimeUpdatesRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String? gameName});
}

/// @nodoc
class __$SubscribeToRealTimeUpdatesRequestCopyWithImpl<$Res>
    implements _$SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  __$SubscribeToRealTimeUpdatesRequestCopyWithImpl(this._self, this._then);

  final _SubscribeToRealTimeUpdatesRequest _self;
  final $Res Function(_SubscribeToRealTimeUpdatesRequest) _then;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameName = freezed,
  }) {
    return _then(_SubscribeToRealTimeUpdatesRequest(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$UnsubscribeFromRealTimeUpdatesRequest {
  /// Serializes this UnsubscribeFromRealTimeUpdatesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnsubscribeFromRealTimeUpdatesRequest);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UnsubscribeFromRealTimeUpdatesRequest()';
  }
}

/// @nodoc
class $UnsubscribeFromRealTimeUpdatesRequestCopyWith<$Res> {
  $UnsubscribeFromRealTimeUpdatesRequestCopyWith(
      UnsubscribeFromRealTimeUpdatesRequest _,
      $Res Function(UnsubscribeFromRealTimeUpdatesRequest) __);
}

/// @nodoc
@JsonSerializable()
class _UnsubscribeFromRealTimeUpdatesRequest
    implements UnsubscribeFromRealTimeUpdatesRequest {
  const _UnsubscribeFromRealTimeUpdatesRequest();
  factory _UnsubscribeFromRealTimeUpdatesRequest.fromJson(
          Map<String, dynamic> json) =>
      _$UnsubscribeFromRealTimeUpdatesRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    return _$UnsubscribeFromRealTimeUpdatesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UnsubscribeFromRealTimeUpdatesRequest);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UnsubscribeFromRealTimeUpdatesRequest()';
  }
}

/// @nodoc
mixin _$RemoveMatchSourceRequest {
  String get sourceName;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RemoveMatchSourceRequestCopyWith<RemoveMatchSourceRequest> get copyWith =>
      _$RemoveMatchSourceRequestCopyWithImpl<RemoveMatchSourceRequest>(
          this as RemoveMatchSourceRequest, _$identity);

  /// Serializes this RemoveMatchSourceRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RemoveMatchSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName);

  @override
  String toString() {
    return 'RemoveMatchSourceRequest(sourceName: $sourceName)';
  }
}

/// @nodoc
abstract mixin class $RemoveMatchSourceRequestCopyWith<$Res> {
  factory $RemoveMatchSourceRequestCopyWith(RemoveMatchSourceRequest value,
          $Res Function(RemoveMatchSourceRequest) _then) =
      _$RemoveMatchSourceRequestCopyWithImpl;
  @useResult
  $Res call({String sourceName});
}

/// @nodoc
class _$RemoveMatchSourceRequestCopyWithImpl<$Res>
    implements $RemoveMatchSourceRequestCopyWith<$Res> {
  _$RemoveMatchSourceRequestCopyWithImpl(this._self, this._then);

  final RemoveMatchSourceRequest _self;
  final $Res Function(RemoveMatchSourceRequest) _then;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RemoveMatchSourceRequest implements RemoveMatchSourceRequest {
  const _RemoveMatchSourceRequest({required this.sourceName});
  factory _RemoveMatchSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveMatchSourceRequestFromJson(json);

  @override
  final String sourceName;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RemoveMatchSourceRequestCopyWith<_RemoveMatchSourceRequest> get copyWith =>
      __$RemoveMatchSourceRequestCopyWithImpl<_RemoveMatchSourceRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RemoveMatchSourceRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveMatchSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName);

  @override
  String toString() {
    return 'RemoveMatchSourceRequest(sourceName: $sourceName)';
  }
}

/// @nodoc
abstract mixin class _$RemoveMatchSourceRequestCopyWith<$Res>
    implements $RemoveMatchSourceRequestCopyWith<$Res> {
  factory _$RemoveMatchSourceRequestCopyWith(_RemoveMatchSourceRequest value,
          $Res Function(_RemoveMatchSourceRequest) _then) =
      __$RemoveMatchSourceRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String sourceName});
}

/// @nodoc
class __$RemoveMatchSourceRequestCopyWithImpl<$Res>
    implements _$RemoveMatchSourceRequestCopyWith<$Res> {
  __$RemoveMatchSourceRequestCopyWithImpl(this._self, this._then);

  final _RemoveMatchSourceRequest _self;
  final $Res Function(_RemoveMatchSourceRequest) _then;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
  }) {
    return _then(_RemoveMatchSourceRequest(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
