import 'package:get_it/get_it.dart';
import 'package:dauntless/di/modules/network_modules.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/repositories/implementations/file_match_repository.dart';
import 'package:dauntless/repositories/implementations/local_match_repository.dart';
import 'package:dauntless/repositories/implementations/network_match_repository.dart';
import 'package:dauntless/repositories/interfaces/match_persistence_repository_interface.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:dauntless/use_cases/match_management_use_case.dart';
import 'package:dauntless/use_cases/player_slot_management_use_case.dart';
import 'package:dauntless/use_cases/match_source_management_use_case.dart';
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart';
import 'package:injectable/injectable.dart';

/// Dependency injection module for repository interfaces and implementations
/// This module provides the repository layer abstractions for Phase 1
@module
abstract class RepositoryModules {
  
  /// Local match repository implementation
  /// Handles file-based match operations
  @Named('local')
  @Singleton()
  MatchRepositoryInterface localMatchRepository(LoggingUseCase loggingUseCase) {
    return LocalMatchRepository(loggingUseCase.getRemoteLogger('LocalMatchRepository'));
  }

  /// Network match repository implementation
  /// Handles server-based match operations
  @Named('network')
  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<MatchRepositoryInterface> networkMatchRepository(
    ServerRepository serverRepository,
    LoggingUseCase loggingUseCase,
  ) async {
    return NetworkMatchRepository(
      serverRepository,
      loggingUseCase.getRemoteLogger('NetworkMatchRepository'),
    );
  }

  /// File-based match persistence repository
  /// Handles saving/loading matches to/from files
  @Singleton()
  MatchPersistenceRepositoryInterface fileMatchPersistenceRepository(
    LoggingUseCase loggingUseCase,
  ) {
    return FileMatchRepository(loggingUseCase.getRemoteLogger('FileMatchRepository'));
  }

  /// Repository-based local match selection use case
  /// Uses the local match repository interface
  @Named('repositoryLocal')
  @Singleton()
  MatchSelectionUseCase repositoryBasedLocalMatchSelectionUseCase(
    @Named('local') MatchRepositoryInterface localMatchRepository,
    LoggingUseCase loggingUseCase,
  ) {
    return RepositoryBasedMatchSelectionUseCase(
      localMatchRepository,
      loggingUseCase.getRemoteLogger('RepositoryBasedLocalMatchSelectionUseCase'),
    );
  }

  /// Repository-based network match selection use case
  /// Uses the network match repository interface
  @Named('repositoryNetwork')
  @preResolve
  @Singleton()
  @Scope(staticServerConnected)
  Future<MatchSelectionUseCase> repositoryBasedNetworkMatchSelectionUseCase(
    @Named('network') MatchRepositoryInterface networkMatchRepository,
    LoggingUseCase loggingUseCase,
  ) async {
    return RepositoryBasedMatchSelectionUseCase(
      networkMatchRepository,
      loggingUseCase.getRemoteLogger('RepositoryBasedNetworkMatchSelectionUseCase'),
    );
  }

  // ========================================================================
  // PHASE 2: CONSOLIDATED MATCH MANAGEMENT BLOC
  // ========================================================================

  // ========================================================================
  // PHASE 3: MATCH MANAGEMENT USE CASES
  // ========================================================================

  /// Match management use case
  /// Handles match creation, deletion, joining, and leaving
  @Singleton()
  MatchManagementUseCase matchManagementUseCase(
    UserManager userManager,
    LoggingUseCase loggingUseCase,
  ) {
    // Create a function that gets repositories dynamically
    Map<String, MatchRepositoryInterface> getRepositories() {
      final repositories = <String, MatchRepositoryInterface>{};

      // Always try to get local repository
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        repositories['local'] = localRepo;
      } catch (e) {
        // Local repository not available
      }

      // Try to get network repository if in server scope
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          repositories['network'] = networkRepo;
        } catch (e) {
          // Network repository not available
        }
      }

      return repositories;
    }

    return MatchManagementUseCase(
      getRepositories(),
      userManager,
      loggingUseCase.getRemoteLogger('MatchManagementUseCase'),
    );
  }

  /// Player slot management use case
  /// Handles player slot CRUD operations and validation
  @Singleton()
  PlayerSlotManagementUseCase playerSlotManagementUseCase(
    UserManager userManager,
    LoggingUseCase loggingUseCase,
  ) {
    // Create a function that gets repositories dynamically
    Map<String, MatchRepositoryInterface> getRepositories() {
      final repositories = <String, MatchRepositoryInterface>{};

      // Always try to get local repository
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        repositories['local'] = localRepo;
      } catch (e) {
        // Local repository not available
      }

      // Try to get network repository if in server scope
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          repositories['network'] = networkRepo;
        } catch (e) {
          // Network repository not available
        }
      }

      return repositories;
    }

    return PlayerSlotManagementUseCase(
      getRepositories(),
      userManager,
      loggingUseCase.getRemoteLogger('PlayerSlotManagementUseCase'),
    );
  }

  /// Match source management use case
  /// Handles repository lifecycle and source availability
  @Singleton()
  MatchSourceManagementUseCase matchSourceManagementUseCase(
    LoggingUseCase loggingUseCase,
  ) {
    // Create a function that gets repositories dynamically
    Map<String, MatchRepositoryInterface> getRepositories() {
      final repositories = <String, MatchRepositoryInterface>{};

      // Always try to get local repository
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        repositories['local'] = localRepo;
      } catch (e) {
        // Local repository not available
      }

      // Try to get network repository if in server scope
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          repositories['network'] = networkRepo;
        } catch (e) {
          // Network repository not available
        }
      }

      return repositories;
    }

    return MatchSourceManagementUseCase(
      getRepositories(),
      loggingUseCase.getRemoteLogger('MatchSourceManagementUseCase'),
    );
  }

  /// Match data synchronization use case
  /// Handles match data loading, caching, and real-time updates
  @Singleton()
  MatchDataSynchronizationUseCase matchDataSynchronizationUseCase(
    LoggingUseCase loggingUseCase,
  ) {
    return MatchDataSynchronizationUseCase(
      loggingUseCase.getRemoteLogger('MatchDataSynchronizationUseCase'),
    );
  }

  /// Consolidated MatchManagementBloc that replaces individual BLoCs
  /// Uses repository interfaces for data access
  @Singleton()
  MatchManagementBloc matchManagementBloc(
    GameConfigUseCase gameConfigUseCase,
    LoggingUseCase loggingUseCase,
    MatchManagementUseCase matchManagementUseCase,
    PlayerSlotManagementUseCase playerSlotManagementUseCase,
    MatchSourceManagementUseCase matchSourceManagementUseCase,
    MatchDataSynchronizationUseCase matchDataSynchronizationUseCase,
  ) {
    return MatchManagementBloc(
      gameConfigUseCase,
      loggingUseCase.getRemoteLogger('MatchManagementBloc'),
      matchManagementUseCase,
      playerSlotManagementUseCase,
      matchSourceManagementUseCase,
      matchDataSynchronizationUseCase,
    );
  }
}
