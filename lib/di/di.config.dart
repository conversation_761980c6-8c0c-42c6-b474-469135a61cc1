// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dauntless/api/dauntless_api.dart' as _i573;
import 'package:dauntless/data_services/json_read_write_data_service.dart'
    as _i655;
import 'package:dauntless/debug/global_bloc_observer.dart' as _i683;
import 'package:dauntless/di/modules/config_modules.dart' as _i370;
import 'package:dauntless/di/modules/debug_modules.dart' as _i13;
import 'package:dauntless/di/modules/game_top_level_modules.dart' as _i1030;
import 'package:dauntless/di/modules/liberator_modules.dart' as _i447;
import 'package:dauntless/di/modules/network_modules.dart' as _i593;
import 'package:dauntless/di/modules/repository_modules.dart' as _i541;
import 'package:dauntless/frameworks/assets/assets_manager.dart' as _i853;
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart'
    as _i666;
import 'package:dauntless/frameworks/game_config/game_config_manager.dart'
    as _i1020;
import 'package:dauntless/frameworks/game_match/game_match_manager.dart'
    as _i796;
import 'package:dauntless/frameworks/match_save/match_save_manager.dart'
    as _i981;
import 'package:dauntless/frameworks/network/websocket_manager.dart' as _i357;
import 'package:dauntless/frameworks/user/user_manager.dart' as _i764;
import 'package:dauntless/repositories/actions_repository.dart' as _i26;
import 'package:dauntless/repositories/app_life_cycle_repository.dart' as _i766;
import 'package:dauntless/repositories/assets_repository.dart' as _i348;
import 'package:dauntless/repositories/file_selector_repository.dart' as _i43;
import 'package:dauntless/repositories/game_config_repository.dart' as _i42;
import 'package:dauntless/repositories/generic_card_class_repository.dart'
    as _i754;
import 'package:dauntless/repositories/interfaces/match_persistence_repository_interface.dart'
    as _i272;
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart'
    as _i829;
import 'package:dauntless/repositories/locations_repository.dart' as _i938;
import 'package:dauntless/repositories/logging/logging_repository.dart'
    as _i547;
import 'package:dauntless/repositories/match_repository.dart' as _i508;
import 'package:dauntless/repositories/players_repository.dart' as _i694;
import 'package:dauntless/repositories/save_state_repository.dart' as _i504;
import 'package:dauntless/repositories/server_repository.dart' as _i468;
import 'package:dauntless/repositories/theme_repository.dart' as _i402;
import 'package:dauntless/repositories/vehicles_repository.dart' as _i576;
import 'package:dauntless/repositories/websocket_repository.dart' as _i395;
import 'package:dauntless/ui/blocs/theme/theme_bloc.dart' as _i666;
import 'package:dauntless/ui/liberator/blocs/command_center/command_center_bloc.dart'
    as _i618;
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart'
    as _i1034;
import 'package:dauntless/use_cases/actions_use_case.dart' as _i317;
import 'package:dauntless/use_cases/assets_use_case.dart' as _i796;
import 'package:dauntless/use_cases/file_selector_use_case.dart' as _i209;
import 'package:dauntless/use_cases/game_config_use_case.dart' as _i59;
import 'package:dauntless/use_cases/generic_card_class_use_case.dart' as _i1004;
import 'package:dauntless/use_cases/groupings_use_case.dart' as _i533;
import 'package:dauntless/use_cases/locations_use_case.dart' as _i247;
import 'package:dauntless/use_cases/logging_use_case.dart' as _i97;
import 'package:dauntless/use_cases/map_grid_use_case.dart' as _i28;
import 'package:dauntless/use_cases/match/match_use_case.dart' as _i985;
import 'package:dauntless/use_cases/match/network_match_use_case.dart' as _i738;
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart'
    as _i49;
import 'package:dauntless/use_cases/match_management_use_case.dart' as _i418;
import 'package:dauntless/use_cases/match_selection_use_case.dart' as _i496;
import 'package:dauntless/use_cases/match_source_management_use_case.dart'
    as _i603;
import 'package:dauntless/use_cases/player_slot_management_use_case.dart'
    as _i807;
import 'package:dauntless/use_cases/players_use_case.dart' as _i709;
import 'package:dauntless/use_cases/save_state_use_case.dart' as _i774;
import 'package:dauntless/use_cases/server_environment_use_case.dart' as _i14;
import 'package:dauntless/use_cases/server_notifications_use_case.dart'
    as _i489;
import 'package:dauntless/use_cases/theme_use_case.dart' as _i121;
import 'package:dauntless/use_cases/user_use_case.dart' as _i1059;
import 'package:dauntless/use_cases/vehicles_use_case.dart' as _i382;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final gameTopLevelModules = _$GameTopLevelModules();
    final liberatorModules = _$LiberatorModules();
    final networkModules = _$NetworkModules();
    final configModules = _$ConfigModules();
    final debugModules = _$DebugModules();
    final repositoryModules = _$RepositoryModules();
    await gh.factoryAsync<_i694.PlayersRepository>(
      () => gameTopLevelModules.playersRepositoryFactory(),
      preResolve: true,
    );
    await gh.factoryAsync<_i26.ActionsRepository>(
      () => liberatorModules.actionsRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i395.WebSocketRepository>(
      () => networkModules.webSocketRepositoryFactory(),
      preResolve: true,
      dispose: (i) => i.dispose(),
    );
    await gh.singletonAsync<_i14.ServerEnvironmentUseCase>(
      () => networkModules.serverEnvironmentUseCaseFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i402.ThemeRepository>(
      () => configModules.themeRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i655.JsonReadWriteDataService>(
      () => configModules.jsonReadWriteRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i43.FileSelectorRepository>(
      () => configModules.fileSelectorRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i42.GameConfigRepository>(
      () => configModules.gameConfigRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i348.AssetsRepository>(
      () => configModules.assetsRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i1059.UserUseCase>(
      () => configModules.userUseCaseFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i547.LoggingRepository>(
      () => debugModules.loggingRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i504.SaveStateRepository>(
      () => gameTopLevelModules.saveStateRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i754.GenericCardClassRepository>(
      () => gameTopLevelModules.genericCardClassRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i508.MatchRepository>(
      () => gameTopLevelModules.matchRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i28.MapGridUseCase>(
      () => gameTopLevelModules.mapGridUseCaseFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i938.LocationsRepository>(
      () => liberatorModules.locationsRepositoryFactory(),
      preResolve: true,
    );
    await gh.singletonAsync<_i576.VehiclesRepository>(
      () => liberatorModules.vehiclesRepositoryFactory(),
      preResolve: true,
    );
    await gh.factoryAsync<_i247.LocationsUseCase>(
      () => liberatorModules
          .locationsUseCaseFactory(gh<_i938.LocationsRepository>()),
      preResolve: true,
    );
    await gh.factoryAsync<_i533.LocationGroupingsUseCase>(
      () => liberatorModules
          .locationGroupingsUseCaseFactory(gh<_i938.LocationsRepository>()),
      preResolve: true,
    );
    await gh.factoryAsync<_i533.ActionsGroupingsUseCase>(
      () => liberatorModules
          .actionGroupingsUseCaseFactory(gh<_i26.ActionsRepository>()),
      preResolve: true,
    );
    await gh.factoryAsync<_i317.ActionsUseCase>(
      () =>
          liberatorModules.actionsUseCaseFactory(gh<_i26.ActionsRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i209.FileSelectorUseCase>(
      () => configModules
          .fileSelectorUseCaseFactory(gh<_i43.FileSelectorRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i121.ThemeUseCase>(
      () => configModules.themeUseCaseFactory(gh<_i402.ThemeRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i489.ServerNotificationsUseCase>(
      () => networkModules
          .serverNotificationsUseCaseFactory(gh<_i395.WebSocketRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i97.LoggingUseCase>(
      () => debugModules.loggingUseCaseFactory(gh<_i547.LoggingRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i709.PlayersUseCase>(
      () => gameTopLevelModules
          .playersUseCaseFactory(gh<_i694.PlayersRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i796.AssetsUseCase>(
      () => configModules.assetsUseCaseFactory(gh<_i348.AssetsRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i985.MatchUseCase>(
      () => gameTopLevelModules.matchUseCaseFactory(
        gh<_i97.LoggingUseCase>(),
        gh<_i694.PlayersRepository>(),
        gh<_i508.MatchRepository>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i764.UserManager>(
      () => gameTopLevelModules.userManagerFactory(gh<_i1059.UserUseCase>()),
      preResolve: true,
    );
    await gh.factoryAsync<_i382.VehiclesUseCase>(
      () => liberatorModules
          .vehiclesUseCaseFactory(gh<_i576.VehiclesRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i1004.GenericCardClassUseCase>(
      () => gameTopLevelModules.genericCardClassUseCaseFactory(
          gh<_i754.GenericCardClassRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i666.ThemeBloc>(
      () => configModules.themeBlocFactory(gh<_i121.ThemeUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i59.GameConfigUseCase>(
      () => configModules
          .gameConfigUseCaseFactory(gh<_i42.GameConfigRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i774.MatchSaveUseCase>(
      () => gameTopLevelModules
          .saveStateUseCaseFactory(gh<_i504.SaveStateRepository>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i796.GameMatchManager>(
      () => gameTopLevelModules.matchManagerFactory(
        gh<_i985.MatchUseCase>(),
        gh<_i709.PlayersUseCase>(),
        gh<_i1004.GenericCardClassUseCase>(),
        gh<_i247.LocationsUseCase>(),
        gh<_i382.VehiclesUseCase>(),
        gh<_i97.LoggingUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i666.ServerEnvironmentManager>(
      () => networkModules.serverEnvironmentManagerFactory(
        gh<_i14.ServerEnvironmentUseCase>(),
        gh<_i489.ServerNotificationsUseCase>(),
      ),
      preResolve: true,
    );
    await gh.singletonAsync<_i357.WebSocketManager>(
      () => networkModules
          .webSocketManagerFactory(gh<_i489.ServerNotificationsUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i853.AssetsManager>(
      () => configModules.assetsManagerFactory(
        gh<_i796.AssetsUseCase>(),
        gh<_i247.LocationsUseCase>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i272.MatchPersistenceRepositoryInterface>(() =>
        repositoryModules
            .fileMatchPersistenceRepository(gh<_i97.LoggingUseCase>()));
    gh.singleton<_i603.MatchSourceManagementUseCase>(() => repositoryModules
        .matchSourceManagementUseCase(gh<_i97.LoggingUseCase>()));
    gh.singleton<_i49.MatchDataSynchronizationUseCase>(() => repositoryModules
        .matchDataSynchronizationUseCase(gh<_i97.LoggingUseCase>()));
    await gh.singletonAsync<_i766.AppLifeCycleRepository>(
      () => configModules
          .appLifeCycleRepositoryFactory(gh<_i97.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i683.GlobalBlocObserver>(
      () => debugModules.globalBlocObserverFactory(gh<_i97.LoggingUseCase>()),
      preResolve: true,
    );
    await gh.singletonAsync<_i1020.GameConfigManager>(
      () =>
          configModules.gameConfigManagerFactory(gh<_i59.GameConfigUseCase>()),
      preResolve: true,
    );
    gh.singleton<_i829.MatchRepositoryInterface>(
      () => repositoryModules.localMatchRepository(gh<_i97.LoggingUseCase>()),
      instanceName: 'local',
    );
    gh.singleton<_i496.MatchSelectionUseCase>(
      () => repositoryModules.repositoryBasedLocalMatchSelectionUseCase(
        gh<_i829.MatchRepositoryInterface>(instanceName: 'local'),
        gh<_i97.LoggingUseCase>(),
      ),
      instanceName: 'repositoryLocal',
    );
    gh.singleton<_i418.MatchManagementUseCase>(
        () => repositoryModules.matchManagementUseCase(
              gh<_i764.UserManager>(),
              gh<_i97.LoggingUseCase>(),
            ));
    gh.singleton<_i807.PlayerSlotManagementUseCase>(
        () => repositoryModules.playerSlotManagementUseCase(
              gh<_i764.UserManager>(),
              gh<_i97.LoggingUseCase>(),
            ));
    gh.singleton<_i1034.MatchManagementBloc>(
        () => repositoryModules.matchManagementBloc(
              gh<_i59.GameConfigUseCase>(),
              gh<_i97.LoggingUseCase>(),
              gh<_i418.MatchManagementUseCase>(),
              gh<_i807.PlayerSlotManagementUseCase>(),
              gh<_i603.MatchSourceManagementUseCase>(),
              gh<_i49.MatchDataSynchronizationUseCase>(),
            ));
    await gh.singletonAsync<_i981.MatchSaveManager>(
      () => gameTopLevelModules.matchStateManagerFactory(
        gh<_i796.GameMatchManager>(),
        gh<_i1020.GameConfigManager>(),
      ),
      preResolve: true,
    );
    gh.singleton<_i618.CommandCenterBloc>(
        () => liberatorModules.commandCenterBlocFactory(
              gh<_i97.LoggingUseCase>(),
              gh<_i796.GameMatchManager>(),
              gh<_i981.MatchSaveManager>(),
            ));
    return this;
  }

// initializes the registration of serverConnected-scope dependencies inside of GetIt
  Future<_i174.GetIt> initServerConnectedScope(
      {_i174.ScopeDisposeFunc? dispose}) async {
    return _i526.GetItHelper(this).initScopeAsync(
      'serverConnected',
      dispose: dispose,
      init: (_i526.GetItHelper gh) async {
        final networkModules = _$NetworkModules();
        final repositoryModules = _$RepositoryModules();
        await gh.singletonAsync<_i573.DauntlessApi>(
          () => networkModules
              .buildDauntlessApi(gh<_i666.ServerEnvironmentManager>()),
          preResolve: true,
        );
        await gh.singletonAsync<_i468.ServerRepository>(
          () =>
              networkModules.serverRepositoryFactory(gh<_i573.DauntlessApi>()),
          preResolve: true,
        );
        await gh.singletonAsync<_i829.MatchRepositoryInterface>(
          () => repositoryModules.networkMatchRepository(
            gh<_i468.ServerRepository>(),
            gh<_i97.LoggingUseCase>(),
          ),
          instanceName: 'network',
          preResolve: true,
        );
        await gh.singletonAsync<_i738.NetworkMatchUseCase>(
          () => networkModules.networkMatchUseCaseFactory(
            gh<_i97.LoggingUseCase>(),
            gh<_i694.PlayersRepository>(),
            gh<_i508.MatchRepository>(),
            gh<_i468.ServerRepository>(),
          ),
          preResolve: true,
        );
        await gh.singletonAsync<_i496.MatchSelectionUseCase>(
          () => repositoryModules.repositoryBasedNetworkMatchSelectionUseCase(
            gh<_i829.MatchRepositoryInterface>(instanceName: 'network'),
            gh<_i97.LoggingUseCase>(),
          ),
          instanceName: 'repositoryNetwork',
          preResolve: true,
        );
      },
    );
  }
}

class _$GameTopLevelModules extends _i1030.GameTopLevelModules {}

class _$LiberatorModules extends _i447.LiberatorModules {}

class _$NetworkModules extends _i593.NetworkModules {}

class _$ConfigModules extends _i370.ConfigModules {}

class _$DebugModules extends _i13.DebugModules {}

class _$RepositoryModules extends _i541.RepositoryModules {}
